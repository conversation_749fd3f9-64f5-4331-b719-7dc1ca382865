server:
  port: 8081
spring:
  application:
    name: userservice
  datasource:
    url: ***************************************************************************************************
    username: root
    password: aA13717028793#
    driver-class-name: com.mysql.jdbc.Driver
  cloud:
#    nacos:
#      server-addr: localhost:8848 # nacos地址

#    540
   nacos:
     server-addr: **********:8848
     discovery:
       server-addr: **********:8848
     config:
       server-addr: **********:8848
       file-extension: yaml
       shared-configs:
         - data-id: shared-jdbc.yaml
           refresh: true
           group: DEFAULT_GROUP


  mvc:
    servlet:
      load-on-startup: 1
mybatis:
  type-aliases-package: com.ict.ycwl.user.pojo
  configuration:
    map-underscore-to-camel-case: true
logging:
  level:
    cn.itcast: debug
knife4j:
  enable: false

mybatis-plus:
  global-config:
    db-config:
      id-type: auto
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
jjking:
  dbPath: E:\\www\\wwwroot\\ycwl\\masterDatasource.txt