package com.ict.ycwl.pathcalculate.controller;

import com.ict.ycwl.pathcalculate.service.LegacyTravelTimeGeneratorService;
import com.ict.ycwl.pathcalculate.service.LegacyTravelTimeGeneratorService.GenerationResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 前辈的时间矩阵生成控制器
 * 提供API接口来触发前辈方法的时间矩阵数据生成
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/legacy-travel-time")
@ConditionalOnProperty(name = "legacy.travel-time.enabled", havingValue = "true", matchIfMissing = false)
public class LegacyTravelTimeController {
    
    @Autowired
    private LegacyTravelTimeGeneratorService legacyTravelTimeGeneratorService;
    
    /**
     * 使用前辈的方法生成时间矩阵数据
     * 
     * @return 生成结果
     */
    @PostMapping("/generate")
    public Map<String, Object> generateTravelTimeMatrix() {
        
        log.info("收到前辈方法的时间矩阵生成请求");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 调用前辈的生成方法
            GenerationResult result = legacyTravelTimeGeneratorService.generateTravelTimeMatrix();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            response.put("success", true);
            response.put("message", "前辈方法的时间矩阵生成完成");
            response.put("totalGenerated", result.getTotalGenerated());
            response.put("totalApiCalls", result.getTotalApiCalls());
            response.put("fixedCount", result.getFixedCount());
            response.put("osrmAvailable", result.isOsrmAvailable());
            response.put("errors", result.getErrors());
            response.put("duration", duration);
            response.put("durationMinutes", duration / 1000.0 / 60.0);
            
            log.info("前辈方法的时间矩阵生成完成: 生成{}条记录, 耗时{}分钟", 
                    result.getTotalGenerated(), duration / 1000.0 / 60.0);
            
        } catch (Exception e) {
            log.error("前辈方法的时间矩阵生成失败", e);
            response.put("success", false);
            response.put("message", "生成失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 检查OSRM服务状态
     * 
     * @return OSRM服务状态
     */
    @GetMapping("/osrm-status")
    public Map<String, Object> checkOSRMStatus() {
        log.info("检查OSRM服务状态");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            boolean available = com.ict.ycwl.pathcalculate.v6.OSRMTool.isOSRMServiceAvailable("http://192.168.79.130:5000");
            
            response.put("success", true);
            response.put("osrmAvailable", available);
            response.put("osrmUrl", "http://192.168.79.130:5000");
            response.put("message", available ? "OSRM服务可用" : "OSRM服务不可用，将使用高德API");
            
        } catch (Exception e) {
            log.error("检查OSRM服务状态失败", e);
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
            response.put("osrmAvailable", false);
        }
        
        return response;
    }
    
    /**
     * 获取当前时间矩阵统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getStats() {
        log.info("获取时间矩阵统计信息");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以添加具体的统计查询逻辑
            response.put("success", true);
            response.put("message", "统计信息获取成功");
            response.put("note", "具体统计逻辑待实现");
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "LegacyTravelTimeController");
        response.put("description", "前辈的时间矩阵生成服务");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
    
    /**
     * 获取使用说明
     * 
     * @return 使用说明
     */
    @GetMapping("/usage")
    public Map<String, Object> getUsage() {
        Map<String, Object> response = new HashMap<>();
        response.put("service", "前辈的时间矩阵生成服务");
        response.put("description", "基于前辈RouteTest001代码的时间矩阵生成服务");
        
        Map<String, String> endpoints = new HashMap<>();
        endpoints.put("POST /legacy-travel-time/generate", "生成时间矩阵数据");
        endpoints.put("GET /legacy-travel-time/osrm-status", "检查OSRM服务状态");
        endpoints.put("GET /legacy-travel-time/stats", "获取统计信息");
        endpoints.put("GET /legacy-travel-time/health", "健康检查");
        endpoints.put("GET /legacy-travel-time/usage", "获取使用说明");
        
        response.put("endpoints", endpoints);
        
        Map<String, String> features = new HashMap<>();
        features.put("OSRM批量计算", "优先使用OSRM服务进行批量距离计算");
        features.put("高德API备用", "OSRM不可用时自动切换到高德地图API");
        features.put("智能速度计算", "根据地点类型和是否高速选择不同速度");
        features.put("错误修复", "自动修复无效的时间记录");
        features.put("配送域分批", "按配送域分批处理，避免内存溢出");
        
        response.put("features", features);
        
        return response;
    }
}
