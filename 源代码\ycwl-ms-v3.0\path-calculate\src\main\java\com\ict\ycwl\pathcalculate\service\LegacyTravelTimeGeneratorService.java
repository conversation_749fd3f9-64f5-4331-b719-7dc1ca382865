package com.ict.ycwl.pathcalculate.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import com.ict.ycwl.pathcalculate.mapper.*;
import com.ict.ycwl.pathcalculate.pojo.Accumulation;
import com.ict.ycwl.pathcalculate.pojo.SystemParameter;
import com.ict.ycwl.pathcalculate.pojo.TransitDepot;
import com.ict.ycwl.pathcalculate.v6.OSRMTool;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;

/**
 * 时间矩阵生成服务
 *
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@Service
public class LegacyTravelTimeGeneratorService {

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private SystemParameterMapper systemParameterMapper;

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransitDepotMapper transitDepotMapper;

    // OSRM服务配置
    private static final String OSRM_BASE_URL = "http://192.168.79.130:5000";
    private static final int MAX_COORDINATES_PER_BATCH = 500;
    
    // 高德地图API配置
    private static final String AMAP_API_KEY = "3729e38b382749ba3a10bae7539e0d9a";
    private static final String AMAP_DIRECTION_URL = "https://restapi.amap.com/v3/direction/driving";

    /**
     * 生成完整的时间矩阵数据
     * 这是前辈test06()方法的服务化版本
     * 
     * @return 生成结果统计
     */
    @Transactional
    public GenerationResult generateTravelTimeMatrix() {
        log.info("开始使用前辈的方法生成时间矩阵数据...");
        
        GenerationResult result = new GenerationResult();
        
        try {
            // 1. 检查OSRM服务可用性
            if (!OSRMTool.isOSRMServiceAvailable(OSRM_BASE_URL)) {
                log.warn("OSRM服务不可用，将使用高德地图API作为备用方案");
                result.setOsrmAvailable(false);
            } else {
                log.info("OSRM服务可用，将使用批量计算模式");
                result.setOsrmAvailable(true);
            }
            
            // 2. 准备额外坐标点（中转站坐标）
            List<TransitDepot> transitDepots = transitDepotMapper.selectList(
                    new LambdaQueryWrapper<TransitDepot>().eq(TransitDepot::getIsDelete, 0));
            
            List<Accumulation> extraPoints = new ArrayList<>();
            for (TransitDepot transitDepot : transitDepots) {
                Accumulation accumulation = new Accumulation();
                accumulation.setLongitude(Double.valueOf(transitDepot.getLongitude()));
                accumulation.setLatitude(Double.valueOf(transitDepot.getLatitude()));
                extraPoints.add(accumulation);
            }
            
            log.info("准备了{}个中转站作为额外坐标点", extraPoints.size());
            
            // 3. 获取系统参数
            SystemParameter sp = systemParameterMapper.selectById(1);
            double townshipSpeed = sp.getTownshipRoads();
            double townSpeed = sp.getUrbanRoads();
            double highwaySpeed = sp.getFreeway();
            
            log.info("系统参数 - 乡镇速度: {}km/h, 城镇速度: {}km/h, 高速速度: {}km/h", 
                    townshipSpeed, townSpeed, highwaySpeed);
            
            // 4. 获取所有配送域
            List<String> areas = jdbcTemplate.queryForList(
                    "select delivery_area_name from delivery_area where is_delete=0", String.class);
            areas.add("市辖区"); // 前辈代码中的特殊处理
            
            log.info("找到{}个配送域: {}", areas.size(), areas);
            
            // 5. 按配送域处理
            int totalGenerated = 0;
            int totalApiCalls = 0;
            
            for (String area : areas) {
                log.info("开始处理配送域: {}", area);
                
                try {
                    AreaResult areaResult = processArea(area, extraPoints, townshipSpeed, townSpeed, highwaySpeed);
                    totalGenerated += areaResult.getGeneratedCount();
                    totalApiCalls += areaResult.getApiCallCount();
                    
                    log.info("配送域{}处理完成: 生成{}条记录, API调用{}次", 
                            area, areaResult.getGeneratedCount(), areaResult.getApiCallCount());
                    
                } catch (Exception e) {
                    log.error("处理配送域{}时发生错误: {}", area, e.getMessage(), e);
                    result.addError("配送域" + area + ": " + e.getMessage());
                }
            }
            
            result.setTotalGenerated(totalGenerated);
            result.setTotalApiCalls(totalApiCalls);
            
            // 6. 修复无效记录（前辈的test07方法）
            log.info("开始修复无效记录...");
            int fixedCount = fixInvalidRecords();
            result.setFixedCount(fixedCount);
            
            log.info("时间矩阵生成完成: 总共生成{}条记录, API调用{}次, 修复{}条记录", 
                    totalGenerated, totalApiCalls, fixedCount);
            
        } catch (Exception e) {
            log.error("生成时间矩阵时发生严重错误", e);
            result.addError("系统错误: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * 处理单个配送域
     * 基于前辈代码的核心逻辑
     */
    private AreaResult processArea(String area, List<Accumulation> extraPoints, 
                                 double townshipSpeed, double townSpeed, double highwaySpeed) 
            throws JSONException, ApiKeyException {
        
        AreaResult result = new AreaResult(area);
        
        // 1. 查询当前配送域下所有的聚集区
        LambdaQueryWrapper<Accumulation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Accumulation::getAreaName, area).eq(Accumulation::getIsDelete, false);
        List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
        
        if (accumulations.isEmpty()) {
            log.info("配送域{}没有聚集区，跳过", area);
            return result;
        }
        
        // 2. 合并当前配送域坐标和额外坐标
        List<Accumulation> allPoints = new ArrayList<>(accumulations);
        allPoints.addAll(extraPoints);
        
        log.info("配送域{}: 聚集区{}个, 总坐标点{}个", area, accumulations.size(), allPoints.size());
        
        // 3. 批量获取所有点的类型
        Map<String, String> typeMap = getLocationTypes(allPoints);
        
        // 4. 分批处理坐标点
        int totalPoints = allPoints.size();
        int batches = (int) Math.ceil((double) totalPoints / MAX_COORDINATES_PER_BATCH);
        
        for (int batch = 0; batch < batches; batch++) {
            int startIdx = batch * MAX_COORDINATES_PER_BATCH;
            int endIdx = Math.min(startIdx + MAX_COORDINATES_PER_BATCH, totalPoints);
            List<Accumulation> batchAccumulations = allPoints.subList(startIdx, endIdx);
            
            log.info("处理配送域{}的第{}/{}批，坐标点数: {}", area, batch + 1, batches, batchAccumulations.size());
            
            // 5. 使用OSRM批量计算距离
            List<JSONObject> batchResults = calculateBatchDistances(batchAccumulations);
            
            // 6. 准备批量插入数据
            List<Object[]> batchArgs = new ArrayList<>();
            
            for (JSONObject batchResult : batchResults) {
                int sourceIdx = batchResult.getInt("sourceIdx");
                int destIdx = batchResult.getInt("destIdx");
                Accumulation start = batchAccumulations.get(sourceIdx);
                Accumulation end = batchAccumulations.get(destIdx);
                
                // 跳过额外点之间的组合（前辈的逻辑）
                if (extraPoints.contains(start) && extraPoints.contains(end)) {
                    continue;
                }
                
                double dist = batchResult.getDouble("distance");
                boolean hasHighway = batchResult.getBoolean("hasHighway");
                
                String keyStart = start.getLongitude() + "_" + start.getLatitude();
                String keyEnd = end.getLongitude() + "_" + end.getLatitude();
                String typeStart = typeMap.getOrDefault(keyStart, "1");
                String typeEnd = typeMap.getOrDefault(keyEnd, "1");
                
                double time = calculateTime(dist, hasHighway, typeStart, typeEnd,
                        townshipSpeed, townSpeed, highwaySpeed);
                
                batchArgs.add(new Object[]{
                        start.getLongitude(), start.getLatitude(),
                        end.getLongitude(), end.getLatitude(),
                        time
                });
            }
            
            // 7. 批量插入本批结果
            if (!batchArgs.isEmpty()) {
                String insertSql = "INSERT INTO travel_time " +
                        "(longitude_start, latitude_start, longitude_end, latitude_end, travel_time) " +
                        "VALUES (?, ?, ?, ?, ?)";
                jdbcTemplate.batchUpdate(insertSql, batchArgs);
                result.addGeneratedCount(batchArgs.size());
            }
        }
        
        return result;
    }

    /**
     * 获取所有坐标点的类型
     * 基于前辈的逻辑
     */
    private Map<String, String> getLocationTypes(List<Accumulation> accumulations) {
        Map<String, String> typeMap = new HashMap<>();

        try {
            // 构建查询条件
            StringBuilder coordBuilder = new StringBuilder();
            for (int i = 0; i < accumulations.size(); i++) {
                if (i > 0) coordBuilder.append(" OR ");
                Accumulation acc = accumulations.get(i);
                coordBuilder.append("(longitude = ").append(acc.getLongitude())
                           .append(" AND latitude = ").append(acc.getLatitude()).append(")");
            }

            String sql = "SELECT longitude, latitude, type FROM accumulation WHERE " + coordBuilder.toString();
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);

            for (Map<String, Object> row : results) {
                String key = row.get("longitude") + "_" + row.get("latitude");
                String type = String.valueOf(row.get("type"));
                typeMap.put(key, type);
            }

        } catch (Exception e) {
            log.warn("获取坐标点类型失败，使用默认类型: {}", e.getMessage());
        }

        return typeMap;
    }

    /**
     * 批量计算距离
     * 使用OSRM或高德API
     */
    private List<JSONObject> calculateBatchDistances(List<Accumulation> accumulations) throws JSONException {
        List<JSONObject> results = new ArrayList<>();

        if (OSRMTool.isOSRMServiceAvailable(OSRM_BASE_URL)) {
            // 使用OSRM批量计算
            results = calculateWithOSRM(accumulations);
        } else {
            // 使用高德API逐个计算
            results = calculateWithAmap(accumulations);
        }

        return results;
    }

    /**
     * 使用OSRM批量计算距离
     */
    private List<JSONObject> calculateWithOSRM(List<Accumulation> accumulations) throws JSONException {
        List<JSONObject> results = new ArrayList<>();

        // 构建坐标字符串
        StringBuilder coordBuilder = new StringBuilder();
        for (int i = 0; i < accumulations.size(); i++) {
            if (i > 0) coordBuilder.append(";");
            Accumulation acc = accumulations.get(i);
            coordBuilder.append(acc.getLongitude()).append(",").append(acc.getLatitude());
        }

        String url = OSRMTool.buildTableUrl(OSRM_BASE_URL, coordBuilder.toString());

        try {
            Object response = OSRMTool.getDistancesAndHighwayStatus(url);
            JSONObject jsonResponse = (JSONObject) response;

            JSONArray distances = jsonResponse.getJSONArray("distances");

            for (int i = 0; i < accumulations.size(); i++) {
                JSONArray row = distances.getJSONArray(i);
                for (int j = 0; j < accumulations.size(); j++) {
                    if (i != j) {
                        double distance = row.getDouble(j);

                        JSONObject result = new JSONObject();
                        result.put("sourceIdx", i);
                        result.put("destIdx", j);
                        result.put("distance", distance);
                        result.put("hasHighway", false); // OSRM暂不支持高速检测

                        results.add(result);
                    }
                }
            }

        } catch (Exception e) {
            log.error("OSRM批量计算失败，降级到高德API: {}", e.getMessage());
            return calculateWithAmap(accumulations);
        }

        return results;
    }

    /**
     * 使用高德API计算距离
     */
    private List<JSONObject> calculateWithAmap(List<Accumulation> accumulations) throws JSONException {
        List<JSONObject> results = new ArrayList<>();

        for (int i = 0; i < accumulations.size(); i++) {
            for (int j = 0; j < accumulations.size(); j++) {
                if (i != j) {
                    Accumulation start = accumulations.get(i);
                    Accumulation end = accumulations.get(j);

                    try {
                        JSONObject result = callAmapApi(start, end);
                        result.put("sourceIdx", i);
                        result.put("destIdx", j);
                        results.add(result);

                        // API调用间隔
                        Thread.sleep(100);

                    } catch (Exception e) {
                        log.warn("高德API调用失败: {} -> {}, 错误: {}",
                                start.getLongitude() + "," + start.getLatitude(),
                                end.getLongitude() + "," + end.getLatitude(),
                                e.getMessage());

                        // 使用距离估算作为备用
                        JSONObject result = new JSONObject();
                        result.put("sourceIdx", i);
                        result.put("destIdx", j);
                        result.put("distance", calculateHaversineDistance(start, end) * 1000); // 转为米
                        result.put("hasHighway", false);
                        results.add(result);
                    }
                }
            }
        }

        return results;
    }

    /**
     * 调用高德地图API
     */
    private JSONObject callAmapApi(Accumulation start, Accumulation end) throws Exception {
        String origin = start.getLongitude() + "," + start.getLatitude();
        String destination = end.getLongitude() + "," + end.getLatitude();

        String urlStr = String.format("%s?key=%s&origin=%s&destination=%s&strategy=0&extensions=base",
                AMAP_DIRECTION_URL, AMAP_API_KEY, origin, destination);

        URL url = new URL(urlStr);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(30000);

        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("高德API返回错误码: " + responseCode);
        }

        Scanner scanner = new Scanner(connection.getInputStream());
        StringBuilder response = new StringBuilder();
        while (scanner.hasNextLine()) {
            response.append(scanner.nextLine());
        }
        scanner.close();
        connection.disconnect();

        JSONObject jsonResponse = new JSONObject(response.toString());
        String status = jsonResponse.getString("status");
        if (!"1".equals(status)) {
            throw new RuntimeException("高德API返回错误状态: " + status);
        }

        JSONObject route = jsonResponse.getJSONObject("route");
        JSONArray paths = route.getJSONArray("paths");
        JSONObject firstPath = paths.getJSONObject(0);

        double distance = firstPath.getDouble("distance");
        boolean hasHighway = firstPath.toString().contains("高速") || firstPath.toString().contains("highway");

        JSONObject result = new JSONObject();
        result.put("distance", distance);
        result.put("hasHighway", hasHighway);

        return result;
    }

    /**
     * 计算时间
     * 基于前辈的逻辑
     */
    private double calculateTime(double dist, boolean hasHighway, String typeStart, String typeEnd,
                               double townshipSpeed, double townSpeed, double highwaySpeed) {
        if (hasHighway) {
            return dist / 1000 / highwaySpeed * 60;
        } else {
            double speed;
            if ("1".equals(typeStart) && "1".equals(typeEnd)) {
                speed = townshipSpeed; // 乡镇速度
            } else if ("2".equals(typeStart) && "2".equals(typeEnd)) {
                speed = townSpeed; // 城镇速度
            } else {
                speed = (townshipSpeed + townSpeed) / 2; // 平均速度
            }
            return dist / 1000 / speed * 60;
        }
    }

    /**
     * 修复无效记录
     * 基于前辈的test07方法
     */
    private int fixInvalidRecords() {
        int fixedCount = 0;

        try {
            String sql = "select * from travel_time where travel_time <= 0";
            List<Map<String, Object>> invalidRecords = jdbcTemplate.queryForList(sql);

            log.info("找到{}条无效记录需要修复", invalidRecords.size());

            for (Map<String, Object> record : invalidRecords) {
                try {
                    String lngStart = String.valueOf(record.get("longitude_start"));
                    String latStart = String.valueOf(record.get("latitude_start"));
                    String lngEnd = String.valueOf(record.get("longitude_end"));
                    String latEnd = String.valueOf(record.get("latitude_end"));

                    Accumulation start = new Accumulation();
                    start.setLongitude(Double.valueOf(lngStart));
                    start.setLatitude(Double.valueOf(latStart));

                    Accumulation end = new Accumulation();
                    end.setLongitude(Double.valueOf(lngEnd));
                    end.setLatitude(Double.valueOf(latEnd));

                    JSONObject result = callAmapApi(start, end);
                    double distance = result.getDouble("distance");
                    boolean hasHighway = result.getBoolean("hasHighway");

                    // 使用默认速度计算时间
                    double time = hasHighway ? distance / 1000 / 80 * 60 : distance / 1000 / 40 * 60;

                    String updateSql = "UPDATE travel_time SET travel_time = ? WHERE " +
                            "longitude_start = ? AND latitude_start = ? AND longitude_end = ? AND latitude_end = ?";
                    jdbcTemplate.update(updateSql, time, lngStart, latStart, lngEnd, latEnd);

                    fixedCount++;

                    // API调用间隔
                    Thread.sleep(100);

                } catch (Exception e) {
                    log.warn("修复记录失败: {}", e.getMessage());
                }
            }

        } catch (Exception e) {
            log.error("修复无效记录时发生错误: {}", e.getMessage(), e);
        }

        return fixedCount;
    }

    /**
     * 计算两点间的球面距离（Haversine公式）
     */
    private double calculateHaversineDistance(Accumulation start, Accumulation end) {
        final double R = 6371.0; // 地球半径（公里）

        double dLat = Math.toRadians(end.getLatitude() - start.getLatitude());
        double dLng = Math.toRadians(end.getLongitude() - start.getLongitude());

        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(Math.toRadians(start.getLatitude())) * Math.cos(Math.toRadians(end.getLatitude())) *
                   Math.sin(dLng / 2) * Math.sin(dLng / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        return R * c;
    }

    /**
     * 生成结果统计
     */
    public static class GenerationResult {
        private int totalGenerated = 0;
        private int totalApiCalls = 0;
        private int fixedCount = 0;
        private boolean osrmAvailable = false;
        private List<String> errors = new ArrayList<>();

        public void addError(String error) {
            errors.add(error);
        }

        // Getters and Setters
        public int getTotalGenerated() { return totalGenerated; }
        public void setTotalGenerated(int totalGenerated) { this.totalGenerated = totalGenerated; }

        public int getTotalApiCalls() { return totalApiCalls; }
        public void setTotalApiCalls(int totalApiCalls) { this.totalApiCalls = totalApiCalls; }

        public int getFixedCount() { return fixedCount; }
        public void setFixedCount(int fixedCount) { this.fixedCount = fixedCount; }

        public boolean isOsrmAvailable() { return osrmAvailable; }
        public void setOsrmAvailable(boolean osrmAvailable) { this.osrmAvailable = osrmAvailable; }

        public List<String> getErrors() { return errors; }
    }

    /**
     * 单个配送域的处理结果
     */
    public static class AreaResult {
        private final String areaName;
        private int generatedCount = 0;
        private int apiCallCount = 0;

        public AreaResult(String areaName) {
            this.areaName = areaName;
        }

        public void addGeneratedCount(int count) {
            this.generatedCount += count;
        }

        public void addApiCallCount(int count) {
            this.apiCallCount += count;
        }

        // Getters
        public String getAreaName() { return areaName; }
        public int getGeneratedCount() { return generatedCount; }
        public int getApiCallCount() { return apiCallCount; }
    }
}
