# Spring
spring:
  application:
    # 应用名称
    name: pathcalculate
  cloud:
    nacos:
      server-addr: localhost:8848
      discovery:
        server-addr: localhost:8848
      config:
        server-addr: localhost:8848
        file-extension: yaml
        shared-configs:
          - data-id: shared-jdbc.yaml
            refresh: true
            group: DEFAULT_GROUP


#    nacos:
#      server-addr: **********:8848
#      discovery:
#        server-addr: **********:8848
#      config:
#        server-addr: **********:8848
#        file-extension: yaml

#    # 烟草//数据库：************
#    nacos:
#      server-addr: ************:8848
#      discovery:
#        server-addr: ************:8848
#      config:
#        server-addr: ************:8848
#        file-extension: yaml



