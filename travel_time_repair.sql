-- =====================================================
-- 粤北卷烟物流管理平台 - travel_time表数据修复脚本
-- 目标：将覆盖度从42.90%提升到80%以上，满足算法要求
-- =====================================================

-- 1. 分析当前覆盖度情况
SELECT 
    '=== 当前覆盖度分析 ===' as analysis_type,
    COUNT(*) as current_records,
    (SELECT 
        SUM((acc_count + 1) * acc_count) 
     FROM (
        SELECT 
            td.transit_depot_id,
            td.transit_depot_name,
            COUNT(a.accumulation_id) as acc_count
        FROM transit_depot td 
        LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
        WHERE td.is_delete = 0 
          AND (a.is_delete = 0 OR a.is_delete IS NULL)
          AND (a.longitude != 0.0 OR a.longitude IS NULL) 
          AND (a.latitude != 0.0 OR a.latitude IS NULL)
        GROUP BY td.transit_depot_id, td.transit_depot_name
     ) depot_stats
    ) as expected_total,
    ROUND(
        COUNT(*) * 100.0 / (
            SELECT 
                SUM((acc_count + 1) * acc_count) 
             FROM (
                SELECT 
                    td.transit_depot_id,
                    COUNT(a.accumulation_id) as acc_count
                FROM transit_depot td 
                LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
                WHERE td.is_delete = 0 
                  AND (a.is_delete = 0 OR a.is_delete IS NULL)
                  AND (a.longitude != 0.0 OR a.longitude IS NULL) 
                  AND (a.latitude != 0.0 OR a.latitude IS NULL)
                GROUP BY td.transit_depot_id
             ) depot_stats
        ), 2
    ) as coverage_percent
FROM travel_time;

-- 2. 按中转站分析缺失的点对数
SELECT 
    '=== 各中转站缺失点对分析 ===' as analysis_type,
    depot_id,
    depot_name,
    total_points,
    expected_pairs,
    existing_pairs,
    missing_pairs,
    ROUND(existing_pairs * 100.0 / expected_pairs, 2) as depot_coverage_percent
FROM (
    SELECT 
        td.transit_depot_id as depot_id,
        td.transit_depot_name as depot_name,
        (COUNT(a.accumulation_id) + 1) as total_points,
        (COUNT(a.accumulation_id) + 1) * COUNT(a.accumulation_id) as expected_pairs,
        COALESCE(existing.pair_count, 0) as existing_pairs,
        ((COUNT(a.accumulation_id) + 1) * COUNT(a.accumulation_id) - COALESCE(existing.pair_count, 0)) as missing_pairs
    FROM transit_depot td 
    LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
        AND a.is_delete = 0 
        AND a.longitude != 0.0 
        AND a.latitude != 0.0
    LEFT JOIN (
        -- 统计每个中转站现有的点对数（简化统计，实际应该精确匹配坐标）
        SELECT 
            a1.transit_depot_id,
            COUNT(*) as pair_count
        FROM accumulation a1
        JOIN accumulation a2 ON a1.transit_depot_id = a2.transit_depot_id
        JOIN travel_time tt ON (
            tt.longitude_start = CAST(a1.longitude AS CHAR) 
            AND tt.latitude_start = CAST(a1.latitude AS CHAR)
            AND tt.longitude_end = CAST(a2.longitude AS CHAR) 
            AND tt.latitude_end = CAST(a2.latitude AS CHAR)
        )
        WHERE a1.is_delete = 0 AND a2.is_delete = 0
          AND a1.longitude != 0.0 AND a1.latitude != 0.0
          AND a2.longitude != 0.0 AND a2.latitude != 0.0
          AND a1.accumulation_id != a2.accumulation_id
        GROUP BY a1.transit_depot_id
    ) existing ON td.transit_depot_id = existing.transit_depot_id
    WHERE td.is_delete = 0
    GROUP BY td.transit_depot_id, td.transit_depot_name, existing.pair_count
) depot_analysis
ORDER BY missing_pairs DESC;

-- 3. 创建临时表存储需要生成的点对
DROP TEMPORARY TABLE IF EXISTS temp_missing_pairs;
CREATE TEMPORARY TABLE temp_missing_pairs (
    depot_id BIGINT,
    depot_name VARCHAR(255),
    start_lng VARCHAR(255),
    start_lat VARCHAR(255),
    end_lng VARCHAR(255),
    end_lat VARCHAR(255),
    start_type ENUM('depot', 'accumulation'),
    end_type ENUM('depot', 'accumulation'),
    priority INT DEFAULT 1,
    INDEX idx_depot (depot_id),
    INDEX idx_coords (start_lng, start_lat, end_lng, end_lat)
);

-- 4. 生成缺失的点对数据（按中转站分组）
INSERT INTO temp_missing_pairs (depot_id, depot_name, start_lng, start_lat, end_lng, end_lat, start_type, end_type, priority)
SELECT 
    td.transit_depot_id as depot_id,
    td.transit_depot_name as depot_name,
    start_coords.lng as start_lng,
    start_coords.lat as start_lat,
    end_coords.lng as end_lng,
    end_coords.lat as end_lat,
    start_coords.coord_type as start_type,
    end_coords.coord_type as end_type,
    CASE 
        WHEN start_coords.coord_type = 'depot' OR end_coords.coord_type = 'depot' THEN 1
        ELSE 2
    END as priority
FROM transit_depot td
CROSS JOIN (
    -- 中转站坐标
    SELECT 
        td2.transit_depot_id,
        td2.longitude as lng,
        td2.latitude as lat,
        'depot' as coord_type
    FROM transit_depot td2
    WHERE td2.is_delete = 0 
      AND td2.longitude IS NOT NULL 
      AND td2.latitude IS NOT NULL
      AND td2.longitude != '0.0' 
      AND td2.latitude != '0.0'
    
    UNION ALL
    
    -- 聚集区坐标
    SELECT 
        a.transit_depot_id,
        CAST(a.longitude AS CHAR) as lng,
        CAST(a.latitude AS CHAR) as lat,
        'accumulation' as coord_type
    FROM accumulation a
    WHERE a.is_delete = 0 
      AND a.longitude != 0.0 
      AND a.latitude != 0.0
) start_coords ON td.transit_depot_id = start_coords.transit_depot_id
CROSS JOIN (
    -- 中转站坐标
    SELECT 
        td3.transit_depot_id,
        td3.longitude as lng,
        td3.latitude as lat,
        'depot' as coord_type
    FROM transit_depot td3
    WHERE td3.is_delete = 0 
      AND td3.longitude IS NOT NULL 
      AND td3.latitude IS NOT NULL
      AND td3.longitude != '0.0' 
      AND td3.latitude != '0.0'
    
    UNION ALL
    
    -- 聚集区坐标
    SELECT 
        a2.transit_depot_id,
        CAST(a2.longitude AS CHAR) as lng,
        CAST(a2.latitude AS CHAR) as lat,
        'accumulation' as coord_type
    FROM accumulation a2
    WHERE a2.is_delete = 0 
      AND a2.longitude != 0.0 
      AND a2.latitude != 0.0
) end_coords ON td.transit_depot_id = end_coords.transit_depot_id
WHERE td.is_delete = 0
  AND (start_coords.lng != end_coords.lng OR start_coords.lat != end_coords.lat) -- 排除自环
  AND NOT EXISTS (
      -- 排除已存在的记录
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = start_coords.lng 
        AND tt.latitude_start = start_coords.lat
        AND tt.longitude_end = end_coords.lng 
        AND tt.latitude_end = end_coords.lat
  );

-- 5. 显示需要生成的数据统计
SELECT 
    '=== 需要生成的数据统计 ===' as analysis_type,
    depot_id,
    depot_name,
    COUNT(*) as missing_pairs,
    SUM(CASE WHEN priority = 1 THEN 1 ELSE 0 END) as high_priority_pairs,
    SUM(CASE WHEN priority = 2 THEN 1 ELSE 0 END) as normal_priority_pairs
FROM temp_missing_pairs
GROUP BY depot_id, depot_name
ORDER BY missing_pairs DESC;

-- 6. 总体统计
SELECT 
    '=== 总体修复统计 ===' as analysis_type,
    COUNT(*) as total_missing_pairs,
    (SELECT COUNT(*) FROM travel_time) as current_records,
    (SELECT COUNT(*) FROM travel_time) + COUNT(*) as after_repair_records,
    ROUND(
        ((SELECT COUNT(*) FROM travel_time) + COUNT(*)) * 100.0 / (
            SELECT 
                SUM((acc_count + 1) * acc_count) 
             FROM (
                SELECT 
                    td.transit_depot_id,
                    COUNT(a.accumulation_id) as acc_count
                FROM transit_depot td 
                LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
                WHERE td.is_delete = 0 
                  AND (a.is_delete = 0 OR a.is_delete IS NULL)
                  AND (a.longitude != 0.0 OR a.longitude IS NULL) 
                  AND (a.latitude != 0.0 OR a.latitude IS NULL)
                GROUP BY td.transit_depot_id
             ) depot_stats
        ), 2
    ) as projected_coverage_percent
FROM temp_missing_pairs;

-- =====================================================
-- 第二部分：数据生成和插入
-- =====================================================

-- 7. 创建存储过程来估算行驶时间（基于距离的简化算法）
DELIMITER $$

DROP PROCEDURE IF EXISTS EstimateTravelTime$$
CREATE PROCEDURE EstimateTravelTime(
    IN start_lng DECIMAL(10,6),
    IN start_lat DECIMAL(10,6),
    IN end_lng DECIMAL(10,6),
    IN end_lat DECIMAL(10,6),
    OUT travel_time_minutes DECIMAL(8,2)
)
BEGIN
    DECLARE distance_km DECIMAL(10,3);
    DECLARE avg_speed_kmh DECIMAL(5,2) DEFAULT 45.0; -- 平均速度45km/h

    -- 使用Haversine公式计算距离（简化版）
    SET distance_km = (
        6371 * ACOS(
            COS(RADIANS(start_lat)) *
            COS(RADIANS(end_lat)) *
            COS(RADIANS(end_lng) - RADIANS(start_lng)) +
            SIN(RADIANS(start_lat)) *
            SIN(RADIANS(end_lat))
        )
    );

    -- 计算行驶时间（分钟）
    -- 基础时间 + 距离时间 + 城市道路修正
    SET travel_time_minutes = (
        2.0 +  -- 基础启动时间2分钟
        (distance_km / avg_speed_kmh * 60) + -- 距离时间
        (distance_km * 0.5) -- 城市道路修正系数
    );

    -- 最小时间限制
    IF travel_time_minutes < 1.0 THEN
        SET travel_time_minutes = 1.0;
    END IF;

    -- 最大时间限制（避免异常值）
    IF travel_time_minutes > 300.0 THEN
        SET travel_time_minutes = 300.0;
    END IF;

END$$

DELIMITER ;

-- 8. 批量生成travel_time数据（分批处理避免超时）
-- 优先处理高优先级的点对（涉及中转站的）

-- 8.1 处理高优先级点对（涉及中转站）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    tmp.start_lng,
    tmp.start_lat,
    tmp.end_lng,
    tmp.end_lat,
    CASE
        -- 同一坐标点的时间为0（虽然已排除，但保险起见）
        WHEN tmp.start_lng = tmp.end_lng AND tmp.start_lat = tmp.end_lat THEN 0.0
        -- 使用简化的距离估算公式
        ELSE GREATEST(1.0, LEAST(300.0,
            2.0 + -- 基础时间
            (6371 * ACOS(
                COS(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lng AS DECIMAL(10,6))) - RADIANS(CAST(tmp.start_lng AS DECIMAL(10,6)))) +
                SIN(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                SIN(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6))))
            ) / 45.0 * 60) + -- 距离时间（45km/h平均速度）
            (6371 * ACOS(
                COS(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lng AS DECIMAL(10,6))) - RADIANS(CAST(tmp.start_lng AS DECIMAL(10,6)))) +
                SIN(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                SIN(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6))))
            ) * 0.5) -- 城市道路修正
        ))
    END as travel_time
FROM temp_missing_pairs tmp
WHERE tmp.priority = 1  -- 高优先级（涉及中转站）
LIMIT 50000;  -- 分批处理，避免超时

-- 8.2 处理普通优先级点对（聚集区之间）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    tmp.start_lng,
    tmp.start_lat,
    tmp.end_lng,
    tmp.end_lat,
    CASE
        WHEN tmp.start_lng = tmp.end_lng AND tmp.start_lat = tmp.end_lat THEN 0.0
        ELSE GREATEST(1.0, LEAST(300.0,
            2.0 +
            (6371 * ACOS(
                COS(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lng AS DECIMAL(10,6))) - RADIANS(CAST(tmp.start_lng AS DECIMAL(10,6)))) +
                SIN(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                SIN(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6))))
            ) / 45.0 * 60) +
            (6371 * ACOS(
                COS(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6)))) *
                COS(RADIANS(CAST(tmp.end_lng AS DECIMAL(10,6))) - RADIANS(CAST(tmp.start_lng AS DECIMAL(10,6)))) +
                SIN(RADIANS(CAST(tmp.start_lat AS DECIMAL(10,6)))) *
                SIN(RADIANS(CAST(tmp.end_lat AS DECIMAL(10,6))))
            ) * 0.5)
        ))
    END as travel_time
FROM temp_missing_pairs tmp
WHERE tmp.priority = 2  -- 普通优先级（聚集区之间）
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt
      WHERE tt.longitude_start = tmp.start_lng
        AND tt.latitude_start = tmp.start_lat
        AND tt.longitude_end = tmp.end_lng
        AND tt.latitude_end = tmp.end_lat
  )
LIMIT 100000;  -- 分批处理

-- 9. 验证修复结果
SELECT
    '=== 修复完成后的覆盖度分析 ===' as analysis_type,
    COUNT(*) as total_records,
    (SELECT
        SUM((acc_count + 1) * acc_count)
     FROM (
        SELECT
            td.transit_depot_id,
            COUNT(a.accumulation_id) as acc_count
        FROM transit_depot td
        LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id
        WHERE td.is_delete = 0
          AND (a.is_delete = 0 OR a.is_delete IS NULL)
          AND (a.longitude != 0.0 OR a.longitude IS NULL)
          AND (a.latitude != 0.0 OR a.latitude IS NULL)
        GROUP BY td.transit_depot_id
     ) depot_stats
    ) as expected_total,
    ROUND(
        COUNT(*) * 100.0 / (
            SELECT
                SUM((acc_count + 1) * acc_count)
             FROM (
                SELECT
                    td.transit_depot_id,
                    COUNT(a.accumulation_id) as acc_count
                FROM transit_depot td
                LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id
                WHERE td.is_delete = 0
                  AND (a.is_delete = 0 OR a.is_delete IS NULL)
                  AND (a.longitude != 0.0 OR a.longitude IS NULL)
                  AND (a.latitude != 0.0 OR a.latitude IS NULL)
                GROUP BY td.transit_depot_id
             ) depot_stats
        ), 2
    ) as final_coverage_percent
FROM travel_time;

-- 10. 清理临时表
DROP TEMPORARY TABLE IF EXISTS temp_missing_pairs;

-- 11. 优化travel_time表（可选）
-- OPTIMIZE TABLE travel_time;

SELECT '=== travel_time表修复完成 ===' as completion_message,
       '请测试/calculateAll接口' as next_step;
