<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.SystemParameterMapper">

    <select id="selectSecondByTranId" resultType="Double">
        select transit_time from second_transit where second_transit_id=#{transitDepotId}
    </select>

    <select id="mySelectCount" resultType="int">
        select COUNT(*) from second_transit where second_transit_id=#{transitDepotId}
    </select>

    <select id="mySelectById" resultType="com.ict.ycwl.pathcalculate.pojo.SystemParameter">
        select * from system_parameter where id=#{i}
    </select>
</mapper>