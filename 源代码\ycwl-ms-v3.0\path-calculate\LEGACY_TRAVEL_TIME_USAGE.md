# 前辈的时间矩阵生成方法 - 使用指南

## 🎯 **修复完成状态**

✅ **编译成功** - 所有依赖问题已解决  
✅ **前辈代码保留** - RouteTest001.java 完整保留并修复  
✅ **服务化封装** - LegacyTravelTimeGeneratorService 提供服务接口  
✅ **API接口** - LegacyTravelTimeController 提供REST API  
✅ **OSRM支持** - OSRMTool 工具类支持批量计算  

## 🚀 **快速开始**

### **1. 启动服务**
```bash
cd 源代码/ycwl-ms-v3.0/path-calculate
mvn spring-boot:run
```

### **2. 检查OSRM服务状态**
```bash
curl "http://localhost:8084/legacy-travel-time/osrm-status"
```

### **3. 生成时间矩阵数据**
```bash
# 使用前辈的方法生成时间矩阵
curl -X POST "http://localhost:8084/legacy-travel-time/generate"
```

### **4. 查看服务状态**
```bash
# 健康检查
curl "http://localhost:8084/legacy-travel-time/health"

# 获取使用说明
curl "http://localhost:8084/legacy-travel-time/usage"
```

## 📊 **前辈方法的优势**

### **1. 性能优化**
- **OSRM批量计算**: 使用本地OSRM服务，一次计算多个点对
- **分批处理**: 每批最多500个坐标点，避免内存溢出
- **本地服务**: 无API配额限制，速度快

### **2. 智能降级**
- **优先OSRM**: 首先尝试使用OSRM批量计算
- **高德备用**: OSRM不可用时自动切换到高德地图API
- **距离估算**: API失败时使用Haversine公式估算

### **3. 完整流程**
- **按配送域处理**: 避免内存溢出，支持大规模数据
- **包含中转站**: 将中转站坐标也纳入计算
- **错误修复**: 自动修复无效的时间记录

## 🔧 **技术实现**

### **核心组件**

#### **1. LegacyTravelTimeGeneratorService**
```java
@Service
public class LegacyTravelTimeGeneratorService {
    // 基于前辈RouteTest001的test06()和test07()方法
    public GenerationResult generateTravelTimeMatrix();
}
```

#### **2. OSRMTool**
```java
public class OSRMTool {
    // 封装OSRM服务调用
    public static Object getDistancesAndHighwayStatus(String url);
    public static boolean isOSRMServiceAvailable(String baseUrl);
}
```

#### **3. LegacyTravelTimeController**
```java
@RestController
@RequestMapping("/legacy-travel-time")
public class LegacyTravelTimeController {
    // 提供REST API接口
}
```

### **数据流程**

```
1. 获取所有配送域
   ↓
2. 获取中转站坐标作为额外点
   ↓
3. 按配送域分批处理
   ↓
4. 使用OSRM批量计算距离矩阵
   ↓
5. 根据地点类型和高速状态计算时间
   ↓
6. 批量插入数据库
   ↓
7. 修复无效记录（使用高德API）
```

## 📈 **预期效果**

### **数据规模**
- **配送域**: 11个（南雄市、曲江区、浈江区等）
- **中转站**: 6个
- **聚集区**: 约1,821个
- **预期记录数**: 约67.8万条

### **性能预估**
- **OSRM模式**: 几分钟到几小时（取决于数据规模）
- **高德API模式**: 19-38小时（考虑API限流）
- **混合模式**: 大部分使用OSRM，少量使用高德API修复

## ⚠️ **注意事项**

### **1. OSRM服务依赖**
- **服务地址**: http://192.168.79.130:5000
- **检查方法**: 调用 `/legacy-travel-time/osrm-status` 接口
- **备用方案**: OSRM不可用时自动切换到高德API

### **2. 高德API配额**
- **API密钥**: 3729e38b382749ba3a10bae7539e0d9a
- **调用频率**: 100ms间隔限流
- **用途**: 主要用于修复OSRM无法计算的记录

### **3. 数据库影响**
- **表**: travel_time
- **操作**: INSERT（批量插入）和UPDATE（修复）
- **建议**: 在非业务高峰期执行

## 🔍 **故障排除**

### **常见问题**

#### **1. OSRM服务不可用**
```bash
# 检查OSRM服务状态
curl "http://192.168.79.130:5000/table/v1/driving/113.264385,23.129163;113.280637,23.125178?annotations=distance"

# 如果不可用，服务会自动切换到高德API模式
```

#### **2. 高德API调用失败**
- 检查API密钥是否有效
- 检查网络连接
- 确认API配额是否充足

#### **3. 数据库连接问题**
- 检查数据库连接配置
- 确认数据库服务是否正常

### **监控方法**
```bash
# 查看服务日志
tail -f logs/path-calculate.log | grep "LegacyTravelTime"

# 查看OSRM相关日志
tail -f logs/path-calculate.log | grep "OSRM"
```

## 🎯 **下一步操作**

1. **启动服务**并检查OSRM状态
2. **小规模测试**：先处理一个配送域
3. **监控进度**：观察日志和数据库记录数
4. **验证结果**：检查生成的数据质量
5. **算法测试**：验证路径规划算法是否能正常运行

---

**重要提醒**: 这个方案完全基于前辈的成熟代码，只是进行了必要的修复和服务化封装，保持了原有的核心逻辑和优化策略！
