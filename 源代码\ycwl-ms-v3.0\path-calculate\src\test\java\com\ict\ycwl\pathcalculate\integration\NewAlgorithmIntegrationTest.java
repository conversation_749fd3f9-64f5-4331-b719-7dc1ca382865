package com.ict.ycwl.pathcalculate.integration;

import com.ict.ycwl.pathcalculate.converter.PathPlanningDataConverter;
import com.ict.ycwl.pathcalculate.pojo.ResultRoute;
import com.ict.ycwl.pathcalculate.service.CalculateService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.List;

/**
 * 新算法集成测试
 * 测试重构后的calculateAll方法是否能正常工作
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest(properties = {
        "spring.cloud.nacos.config.enabled=false",
        "spring.cloud.nacos.discovery.enabled=false"
})
@TestPropertySource(properties = {
        "spring.datasource.url=********************************",
        "spring.datasource.username=root",
        "spring.datasource.password=aA13717028793#"
})
public class NewAlgorithmIntegrationTest {

    @Autowired
    private CalculateService calculateService;
    
    @Autowired
    private PathPlanningDataConverter pathPlanningDataConverter;

    /**
     * 测试数据转换器是否能正常加载数据
     */
    @Test
    public void testDataConverterLoading() {
        log.info("=== 测试数据转换器加载 ===");
        
        try {
            com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest request = 
                    pathPlanningDataConverter.loadPathPlanningRequest();
            
            log.info("数据加载成功:");
            log.info("- 聚集区数量: {}", request.getAccumulations().size());
            log.info("- 中转站数量: {}", request.getTransitDepots().size());
            log.info("- 班组数量: {}", request.getTeams().size());
            log.info("- 时间矩阵记录: {}", request.getTimeMatrix().size());
            
            // 验证数据有效性
            assert request.isValid() : "请求数据应该有效";
            assert !request.getAccumulations().isEmpty() : "聚集区数据不应为空";
            assert !request.getTransitDepots().isEmpty() : "中转站数据不应为空";
            assert !request.getTeams().isEmpty() : "班组数据不应为空";
            
            log.info("数据转换器测试通过 ✓");
            
        } catch (Exception e) {
            log.error("数据转换器测试失败", e);
            throw new RuntimeException("数据转换器测试失败", e);
        }
    }

    /**
     * 测试新算法的完整流程
     * 注意：这个测试可能需要较长时间，因为涉及实际的算法计算
     */
    @Test
    public void testNewAlgorithmCalculateAll() {
        log.info("=== 测试新算法完整流程 ===");
        
        try {
            // 记录开始时间
            long startTime = System.currentTimeMillis();
            
            // 调用重构后的calculateAll方法
            List<ResultRoute> results = calculateService.calculateAll("test-api-key");
            
            // 记录结束时间
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            log.info("算法计算完成:");
            log.info("- 生成路线数: {}", results.size());
            log.info("- 计算耗时: {}ms", duration);
            
            // 验证结果
            assert results != null : "结果不应为null";
            assert !results.isEmpty() : "应该生成至少一条路线";
            
            // 验证每条路线的基本信息
            for (int i = 0; i < Math.min(results.size(), 5); i++) {
                ResultRoute route = results.get(i);
                log.info("路线{}: ID={}, 名称={}, 中转站ID={}, 工作时间={}分钟", 
                        i + 1, 
                        route.getRouteId(), 
                        route.getRouteName(), 
                        route.getTransitDepotId(),
                        route.getWorkTime());
                
                assert route.getRouteId() != null : "路线ID不应为null";
                assert route.getRouteName() != null : "路线名称不应为null";
                assert route.getTransitDepotId() != null : "中转站ID不应为null";
            }
            
            log.info("新算法完整流程测试通过 ✓");
            
        } catch (Exception e) {
            log.error("新算法测试失败", e);
            throw new RuntimeException("新算法测试失败", e);
        }
    }

    /**
     * 测试算法结果转换
     */
    @Test
    public void testResultConversion() {
        log.info("=== 测试结果转换 ===");
        
        try {
            // 1. 加载数据
            com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest request = 
                    pathPlanningDataConverter.loadPathPlanningRequest();
            
            // 2. 调用算法
            com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult algorithmResult = 
                    com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtils.calculate(request);
            
            // 3. 转换结果
            List<ResultRoute> resultRoutes = pathPlanningDataConverter.convertToResultRoutes(algorithmResult);
            
            log.info("结果转换完成:");
            log.info("- 算法生成路线: {}", algorithmResult.getRoutes().size());
            log.info("- 转换后路线: {}", resultRoutes.size());
            
            // 验证转换结果
            assert resultRoutes.size() == algorithmResult.getRoutes().size() : "转换前后路线数量应该一致";
            
            for (ResultRoute route : resultRoutes) {
                assert route.getRouteId() != null : "路线ID不应为null";
                assert route.getRouteName() != null : "路线名称不应为null";
                assert route.getTransitDepotId() != null : "中转站ID不应为null";
                assert route.getWorkTime() != null : "工作时间不应为null";
            }
            
            log.info("结果转换测试通过 ✓");
            
        } catch (Exception e) {
            log.error("结果转换测试失败", e);
            throw new RuntimeException("结果转换测试失败", e);
        }
    }

    /**
     * 性能基准测试
     * 比较新旧算法的性能差异（如果需要的话）
     */
    @Test
    public void testPerformanceBenchmark() {
        log.info("=== 性能基准测试 ===");
        
        try {
            int testRounds = 1; // 测试轮数，可以根据需要调整
            long totalTime = 0;
            
            for (int i = 0; i < testRounds; i++) {
                log.info("执行第{}轮测试...", i + 1);
                
                long startTime = System.currentTimeMillis();
                List<ResultRoute> results = calculateService.calculateAll("benchmark-test");
                long endTime = System.currentTimeMillis();
                
                long roundTime = endTime - startTime;
                totalTime += roundTime;
                
                log.info("第{}轮: 生成{}条路线, 耗时{}ms", i + 1, results.size(), roundTime);
            }
            
            double avgTime = (double) totalTime / testRounds;
            log.info("性能基准测试完成:");
            log.info("- 测试轮数: {}", testRounds);
            log.info("- 总耗时: {}ms", totalTime);
            log.info("- 平均耗时: {:.2f}ms", avgTime);
            
            // 性能断言（可以根据实际情况调整）
            assert avgTime < 300000 : "平均计算时间应该在5分钟以内"; // 300秒 = 5分钟
            
            log.info("性能基准测试通过 ✓");
            
        } catch (Exception e) {
            log.error("性能基准测试失败", e);
            throw new RuntimeException("性能基准测试失败", e);
        }
    }
}
