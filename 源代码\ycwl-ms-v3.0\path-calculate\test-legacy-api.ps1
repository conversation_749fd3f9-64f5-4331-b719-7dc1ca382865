# 前辈时间矩阵生成方法测试脚本
Write-Host "========================================" -ForegroundColor Green
Write-Host "前辈的时间矩阵生成方法 - 测试脚本" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$baseUrl = "http://localhost:8084/legacy-travel-time"

# 1. 测试健康检查
Write-Host "1. 检查服务健康状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/health" -UseBasicParsing -TimeoutSec 10
    Write-Host "服务健康检查通过" -ForegroundColor Green
    Write-Host $response.Content
} catch {
    Write-Host "服务健康检查失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请确保服务已启动在端口8084" -ForegroundColor Red
    exit 1
}

Write-Host ""

# 2. 检查OSRM状态
Write-Host "2. 检查OSRM服务状态..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/osrm-status" -UseBasicParsing -TimeoutSec 10
    Write-Host "OSRM状态检查完成" -ForegroundColor Green
    Write-Host $response.Content
} catch {
    Write-Host "OSRM状态检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 3. 获取使用说明
Write-Host "3. 获取使用说明..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "$baseUrl/usage" -UseBasicParsing -TimeoutSec 10
    Write-Host "使用说明获取成功" -ForegroundColor Green
    Write-Host $response.Content
} catch {
    Write-Host "使用说明获取失败: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# 4. 询问是否开始生成
Write-Host "4. 是否开始生成时间矩阵数据？" -ForegroundColor Yellow
Write-Host "注意：这个过程可能需要很长时间（几分钟到几小时）" -ForegroundColor Red
$choice = Read-Host "请输入 y 开始生成，或按任意键取消"

if ($choice -eq "y" -or $choice -eq "Y") {
    Write-Host ""
    Write-Host "开始生成时间矩阵数据..." -ForegroundColor Green
    Write-Host "请耐心等待，可以在服务日志中查看进度..." -ForegroundColor Yellow

    try {
        $response = Invoke-WebRequest -Uri "$baseUrl/generate" -Method POST -UseBasicParsing -TimeoutSec 3600
        Write-Host "时间矩阵生成完成！" -ForegroundColor Green
        Write-Host $response.Content
    } catch {
        Write-Host "时间矩阵生成失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请查看服务日志了解详细错误信息" -ForegroundColor Yellow
    }
} else {
    Write-Host "已取消生成操作" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "测试脚本执行完成" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
