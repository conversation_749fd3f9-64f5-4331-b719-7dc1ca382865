package com.ict.ycwl.pathcalculate.controller;

import com.ict.ycwl.pathcalculate.RouteTest001;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 直接调用前辈原始方法的控制器
 * 完全按照前辈的RouteTest001代码执行
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/original-route")
@ConditionalOnProperty(name = "legacy.travel-time.enabled", havingValue = "true", matchIfMissing = false)
public class OriginalRouteController {
    
    @Autowired
    private RouteTest001 routeTest001;
    
    /**
     * 直接调用前辈的test06方法（主要生成方法）
     */
    @PostMapping("/test06")
    public Map<String, Object> runTest06() {
        log.info("开始执行前辈的test06方法...");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 直接调用前辈的test06方法
            routeTest001.test06();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            response.put("success", true);
            response.put("message", "前辈的test06方法执行完成");
            response.put("duration", duration);
            response.put("durationMinutes", duration / 1000.0 / 60.0);
            
            log.info("前辈的test06方法执行完成，耗时: {}分钟", duration / 1000.0 / 60.0);
            
        } catch (ApiKeyException e) {
            log.error("前辈的test06方法执行失败 - API Key错误", e);
            response.put("success", false);
            response.put("message", "API Key错误: " + e.getMessage());
            response.put("error", "ApiKeyException");
        } catch (JSONException e) {
            log.error("前辈的test06方法执行失败 - JSON解析错误", e);
            response.put("success", false);
            response.put("message", "JSON解析错误: " + e.getMessage());
            response.put("error", "JSONException");
        } catch (Exception e) {
            log.error("前辈的test06方法执行失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 直接调用前辈的test07方法（修复无效记录）
     */
    @PostMapping("/test07")
    public Map<String, Object> runTest07() {
        log.info("开始执行前辈的test07方法...");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 直接调用前辈的test07方法
            routeTest001.test07();
            
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            response.put("success", true);
            response.put("message", "前辈的test07方法执行完成");
            response.put("duration", duration);
            response.put("durationMinutes", duration / 1000.0 / 60.0);
            
            log.info("前辈的test07方法执行完成，耗时: {}分钟", duration / 1000.0 / 60.0);
            
        } catch (ApiKeyException e) {
            log.error("前辈的test07方法执行失败 - API Key错误", e);
            response.put("success", false);
            response.put("message", "API Key错误: " + e.getMessage());
            response.put("error", "ApiKeyException");
        } catch (Exception e) {
            log.error("前辈的test07方法执行失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 按前辈的顺序执行：先test06，再test07
     */
    @PostMapping("/run-all")
    public Map<String, Object> runAll() {
        log.info("开始按前辈的顺序执行：test06 -> test07");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            long totalStartTime = System.currentTimeMillis();
            
            // 1. 先执行test06
            log.info("第1步：执行前辈的test06方法");
            long test06Start = System.currentTimeMillis();
            routeTest001.test06();
            long test06End = System.currentTimeMillis();
            
            // 2. 再执行test07
            log.info("第2步：执行前辈的test07方法");
            long test07Start = System.currentTimeMillis();
            routeTest001.test07();
            long test07End = System.currentTimeMillis();
            
            long totalEndTime = System.currentTimeMillis();
            
            response.put("success", true);
            response.put("message", "前辈的完整流程执行完成");
            response.put("test06Duration", (test06End - test06Start) / 1000.0 / 60.0);
            response.put("test07Duration", (test07End - test07Start) / 1000.0 / 60.0);
            response.put("totalDuration", (totalEndTime - totalStartTime) / 1000.0 / 60.0);
            
            log.info("前辈的完整流程执行完成，总耗时: {}分钟", 
                    (totalEndTime - totalStartTime) / 1000.0 / 60.0);
            
        } catch (Exception e) {
            log.error("前辈的完整流程执行失败", e);
            response.put("success", false);
            response.put("message", "执行失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 获取前辈方法的说明
     */
    @GetMapping("/info")
    public Map<String, Object> getInfo() {
        Map<String, Object> response = new HashMap<>();
        response.put("description", "直接调用前辈RouteTest001的原始方法");
        response.put("test06", "前辈的主要生成方法，使用OSRM批量计算距离矩阵");
        response.put("test07", "前辈的修复方法，使用高德API修复无效记录");
        response.put("runAll", "按前辈的顺序执行完整流程：test06 -> test07");
        response.put("note", "这是前辈的原始代码，未做任何修改");
        return response;
    }
}
