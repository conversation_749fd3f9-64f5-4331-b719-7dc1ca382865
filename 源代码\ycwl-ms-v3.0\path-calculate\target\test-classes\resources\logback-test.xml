<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志文件输出路径 -->
    <property name="LOG_PATH" value="F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs"/>
    <!-- 关闭冗余的调试日志 -->
    <logger name="com.ict.ycwl.pathcalculate.algorithm.core.WorkloadBalancedKMeans" level="OFF" />
    <logger name="com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem" level="INFO" />
    <logger name="com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.UnifiedConstraintModel" level="INFO" />
    <logger name="com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.JSPRITVRPOptimizer" level="INFO" />
    <logger name="com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback" level="INFO" />
    <logger name="com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment" level="INFO" />
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>

    <!-- 测试日志文件输出 -->
    <appender name="TEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/test-execution.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/test-execution.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>50MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>7</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 算法专用日志文件 -->
    <appender name="ALGORITHM_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/algorithm-debug.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/algorithm-debug.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- PathPlanningUtilsTest专用日志文件 -->
    <appender name="TEST_CLASS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/PathPlanningUtilsTest.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/PathPlanningUtilsTest.%d{yyyy-MM-dd-HH}.log</fileNamePattern>
            <maxHistory>24</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 聚类二次优化日志文件 -->
    <appender name="CLUSTERING_POST_OPT_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/clustering-post-optimization.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %-5level [%thread] %logger{36} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/clustering-post-optimization.%d{yyyy-MM-dd-HH}.log</fileNamePattern>
            <maxHistory>24</maxHistory>
        </rollingPolicy>
    </appender>

    <!-- 专门针对PathPlanningUtilsTest的logger配置 -->
    <logger name="com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtilsTest" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TEST_CLASS_FILE"/>
        <appender-ref ref="TEST_FILE"/>
    </logger>

    <!-- 算法包的详细日志 -->
    <logger name="com.ict.ycwl.pathcalculate.algorithm" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ALGORITHM_FILE"/>
    </logger>

    <!-- 聚类二次优化相关日志 -->
    <logger name="com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="CLUSTERING_POST_OPT_FILE"/>
        <appender-ref ref="ALGORITHM_FILE"/>
    </logger>

    <!-- Spring Boot Test相关日志 -->
    <logger name="org.springframework.boot.test" level="INFO"/>
    <logger name="org.springframework.test" level="INFO"/>
    <logger name="org.optaplanner" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ALGORITHM_FILE"/>
    </logger>

    <!-- 根日志配置 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TEST_FILE"/>
    </root>

    <!-- 测试环境专用配置 -->
    <springProfile name="test">
        <logger name="com.ict.ycwl.pathcalculate" level="DEBUG"/>
    </springProfile>
</configuration>