package com.ict.ycwl.pathcalculate.service;

import com.ict.ycwl.pathcalculate.service.TravelTimeGeneratorService.GenerationResult;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 时间矩阵生成服务测试
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest(properties = {
        "spring.cloud.nacos.config.enabled=false",
        "spring.cloud.nacos.discovery.enabled=false"
})
@TestPropertySource(properties = {
        "spring.datasource.url=********************************",
        "spring.datasource.username=root",
        "spring.datasource.password=aA13717028793#",
        "jjking.dbPath=test-path",
        "amap.api.key=3729e38b382749ba3a10bae7539e0d9a"
})
public class TravelTimeGeneratorTest {

    @Autowired
    private TravelTimeGeneratorService travelTimeGeneratorService;
    
    @Autowired
    private AmapApiService amapApiService;

    /**
     * 测试高德地图API连接
     */
    @Test
    public void testAmapApiConnection() {
        log.info("=== 测试高德地图API连接 ===");
        
        try {
            // 使用广州市中心附近的两个点测试
            double fromLng = 113.264385;
            double fromLat = 23.129163;
            double toLng = 113.280637;
            double toLat = 23.125178;
            
            AmapApiService.RouteInfo routeInfo = amapApiService.getRouteInfo(fromLng, fromLat, toLng, toLat);
            
            log.info("API测试结果:");
            log.info("- 行驶时间: {:.2f}分钟", routeInfo.getTravelTime());
            log.info("- 距离: {:.2f}公里", routeInfo.getDistance());
            log.info("- 数据来源: {}", routeInfo.isFromApi() ? "高德API" : "距离估算");
            
            assert routeInfo.getTravelTime() > 0 : "行驶时间应该大于0";
            assert routeInfo.getDistance() > 0 : "距离应该大于0";
            
            log.info("高德地图API连接测试通过 ✓");
            
        } catch (Exception e) {
            log.error("高德地图API连接测试失败", e);
            throw new RuntimeException("API连接测试失败", e);
        }
    }

    /**
     * 测试单个中转站的时间矩阵生成（小规模测试）
     * 注意：这个测试会调用真实的高德API，请确保API配额充足
     */
    @Test
    public void testSingleDepotGeneration() {
        log.info("=== 测试单个中转站时间矩阵生成 ===");
        
        try {
            // 这里只是测试框架，实际需要选择一个聚集区数量较少的中转站进行测试
            // 比如中转站1（118个聚集区）相对较小
            
            log.info("注意：此测试需要调用大量高德API，建议在API配额充足时运行");
            log.info("预计API调用次数：约 119 × 118 = 14,042 次");
            log.info("预计耗时：约 23-47 分钟（考虑API限流）");
            
            // 如果要实际运行，取消下面的注释
            /*
            GenerationResult result = travelTimeGeneratorService.generateAllTravelTimeMatrix(false);
            
            log.info("生成结果:");
            log.info("- 处理中转站数: {}", result.getTotalDepots());
            log.info("- 生成记录数: {}", result.getTotalGenerated());
            log.info("- API调用次数: {}", result.getTotalApiCalls());
            log.info("- 估算次数: {}", result.getTotalEstimated());
            log.info("- 错误数: {}", result.getErrors().size());
            
            if (!result.getErrors().isEmpty()) {
                log.warn("生成过程中的错误:");
                for (String error : result.getErrors()) {
                    log.warn("- {}", error);
                }
            }
            
            assert result.getTotalGenerated() > 0 : "应该生成至少一条记录";
            */
            
            log.info("单个中转站时间矩阵生成测试框架准备完成 ✓");
            
        } catch (Exception e) {
            log.error("单个中转站时间矩阵生成测试失败", e);
            throw new RuntimeException("生成测试失败", e);
        }
    }

    /**
     * 测试时间矩阵数据验证
     */
    @Test
    public void testTravelTimeValidation() {
        log.info("=== 测试时间矩阵数据验证 ===");
        
        try {
            // 测试数据转换器是否能正常工作
            com.ict.ycwl.pathcalculate.converter.PathPlanningDataConverter converter = 
                    new com.ict.ycwl.pathcalculate.converter.PathPlanningDataConverter();
            
            // 这里需要手动注入依赖，或者使用@Autowired
            // converter.loadPathPlanningRequest();
            
            log.info("时间矩阵数据验证测试准备完成 ✓");
            
        } catch (Exception e) {
            log.error("时间矩阵数据验证测试失败", e);
            throw new RuntimeException("验证测试失败", e);
        }
    }

    /**
     * 性能基准测试
     */
    @Test
    public void testPerformanceBenchmark() {
        log.info("=== 性能基准测试 ===");
        
        try {
            // 测试API调用性能
            long startTime = System.currentTimeMillis();
            
            // 调用10次API测试平均响应时间
            int testCount = 5;
            double totalTime = 0;
            
            for (int i = 0; i < testCount; i++) {
                long callStart = System.currentTimeMillis();
                
                AmapApiService.RouteInfo routeInfo = amapApiService.getRouteInfo(
                        113.264385, 23.129163, 113.280637, 23.125178);
                
                long callEnd = System.currentTimeMillis();
                double callTime = (callEnd - callStart) / 1000.0;
                totalTime += callTime;
                
                log.info("第{}次API调用: {:.2f}秒, 结果: {:.2f}分钟", 
                        i + 1, callTime, routeInfo.getTravelTime());
            }
            
            double avgTime = totalTime / testCount;
            long endTime = System.currentTimeMillis();
            
            log.info("性能基准测试结果:");
            log.info("- 测试次数: {}", testCount);
            log.info("- 总耗时: {:.2f}秒", (endTime - startTime) / 1000.0);
            log.info("- 平均API响应时间: {:.2f}秒", avgTime);
            log.info("- 预估完整生成耗时: {:.1f}小时", (678460 * avgTime) / 3600.0);
            
            log.info("性能基准测试完成 ✓");
            
        } catch (Exception e) {
            log.error("性能基准测试失败", e);
            throw new RuntimeException("性能测试失败", e);
        }
    }

    /**
     * 数据库连接和基础查询测试
     */
    @Test
    public void testDatabaseConnection() {
        log.info("=== 测试数据库连接和基础查询 ===");
        
        try {
            // 这里可以添加基础的数据库查询测试
            log.info("数据库连接测试准备完成 ✓");
            
        } catch (Exception e) {
            log.error("数据库连接测试失败", e);
            throw new RuntimeException("数据库测试失败", e);
        }
    }
}
