package com.ict.ycwl.pathcalculate.controller;

import com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper;
import com.ict.ycwl.pathcalculate.service.TravelTimeGeneratorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 时间矩阵数据修复控制器
 * 提供API接口来触发时间矩阵数据的生成和修复
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/travel-time")
public class TravelTimeController {
    
    @Autowired
    private TravelTimeGeneratorService travelTimeGeneratorService;
    
    @Autowired
    private TravelTimeMapper travelTimeMapper;
    
    /**
     * 生成完整的时间矩阵数据
     * 
     * @param forceRegenerate 是否强制重新生成（删除现有数据）
     * @return 生成结果
     */
    @PostMapping("/generate")
    public Map<String, Object> generateTravelTimeMatrix(
            @RequestParam(defaultValue = "false") boolean forceRegenerate) {
        
        log.info("收到时间矩阵生成请求，强制重新生成: {}", forceRegenerate);
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 异步执行生成任务
            CompletableFuture<TravelTimeGeneratorService.GenerationResult> future = 
                    CompletableFuture.supplyAsync(() -> 
                            travelTimeGeneratorService.generateAllTravelTimeMatrix(forceRegenerate));
            
            // 等待完成（这里可以改为异步返回任务ID，然后通过另一个接口查询进度）
            TravelTimeGeneratorService.GenerationResult result = future.get();
            
            response.put("success", true);
            response.put("message", "时间矩阵生成完成");
            response.put("totalDepots", result.getTotalDepots());
            response.put("totalGenerated", result.getTotalGenerated());
            response.put("totalApiCalls", result.getTotalApiCalls());
            response.put("totalEstimated", result.getTotalEstimated());
            response.put("errors", result.getErrors());
            response.put("depotResults", result.getDepotResults());
            
            log.info("时间矩阵生成完成: 生成{}条记录", result.getTotalGenerated());
            
        } catch (Exception e) {
            log.error("时间矩阵生成失败", e);
            response.put("success", false);
            response.put("message", "生成失败: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
        }
        
        return response;
    }
    
    /**
     * 检查时间矩阵数据覆盖度
     * 
     * @return 覆盖度统计信息
     */
    @GetMapping("/coverage")
    public Map<String, Object> checkCoverage() {
        log.info("检查时间矩阵覆盖度");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 这里可以添加具体的覆盖度检查逻辑
            // 暂时返回基本信息
            response.put("success", true);
            response.put("message", "覆盖度检查功能待实现");
            
        } catch (Exception e) {
            log.error("检查覆盖度失败", e);
            response.put("success", false);
            response.put("message", "检查失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 获取时间矩阵数据统计信息
     * 
     * @return 统计信息
     */
    @GetMapping("/stats")
    public Map<String, Object> getStats() {
        log.info("获取时间矩阵统计信息");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 查询基本统计信息
            long totalRecords = travelTimeMapper.selectCount(null);
            
            response.put("success", true);
            response.put("totalRecords", totalRecords);
            response.put("message", "统计信息获取成功");
            
        } catch (Exception e) {
            log.error("获取统计信息失败", e);
            response.put("success", false);
            response.put("message", "获取失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 清空所有时间矩阵数据
     * 
     * @param confirm 确认参数，必须为"YES"才能执行
     * @return 清空结果
     */
    @DeleteMapping("/clear")
    public Map<String, Object> clearAllData(@RequestParam String confirm) {
        log.warn("收到清空时间矩阵数据请求，确认参数: {}", confirm);
        
        Map<String, Object> response = new HashMap<>();
        
        if (!"YES".equals(confirm)) {
            response.put("success", false);
            response.put("message", "需要确认参数 confirm=YES");
            return response;
        }
        
        try {
            // 删除所有数据
            int deletedCount = travelTimeMapper.delete(null);
            
            response.put("success", true);
            response.put("message", "数据清空完成");
            response.put("deletedCount", deletedCount);
            
            log.warn("时间矩阵数据已清空，删除{}条记录", deletedCount);
            
        } catch (Exception e) {
            log.error("清空数据失败", e);
            response.put("success", false);
            response.put("message", "清空失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 测试高德地图API连接
     * 
     * @return 测试结果
     */
    @GetMapping("/test-api")
    public Map<String, Object> testAmapApi() {
        log.info("测试高德地图API连接");
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 使用固定坐标测试API（广州市中心附近的两个点）
            double fromLng = 113.264385;
            double fromLat = 23.129163;
            double toLng = 113.280637;
            double toLat = 23.125178;
            
            // 这里需要注入AmapApiService来测试
            // AmapApiService.RouteInfo routeInfo = amapApiService.getRouteInfo(fromLng, fromLat, toLng, toLat);
            
            response.put("success", true);
            response.put("message", "API测试功能待完善");
            response.put("testCoordinates", String.format("从(%.6f,%.6f)到(%.6f,%.6f)", fromLng, fromLat, toLng, toLat));
            
        } catch (Exception e) {
            log.error("API测试失败", e);
            response.put("success", false);
            response.put("message", "测试失败: " + e.getMessage());
        }
        
        return response;
    }
    
    /**
     * 健康检查接口
     * 
     * @return 健康状态
     */
    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "TravelTimeController");
        response.put("timestamp", System.currentTimeMillis());
        return response;
    }
}
