<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.PointDistanceMapper">

    <select id="isExists" resultType="int">
        SELECT EXISTS(SELECT 1 FROM point_distance WHERE (origin = #{origin} AND destination = #{destination} AND is_delete = 0)
        ) AS is_exists;
    </select>
</mapper>