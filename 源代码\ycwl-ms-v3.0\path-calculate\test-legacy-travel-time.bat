@echo off
echo ========================================
echo 前辈的时间矩阵生成方法 - 测试脚本
echo ========================================
echo.

echo 1. 检查OSRM服务状态...
curl -s "http://localhost:8084/legacy-travel-time/osrm-status" || echo "服务未启动，请先启动path-calculate服务"
echo.

echo 2. 检查服务健康状态...
curl -s "http://localhost:8084/legacy-travel-time/health"
echo.

echo 3. 获取使用说明...
curl -s "http://localhost:8084/legacy-travel-time/usage"
echo.

echo 4. 是否要开始生成时间矩阵数据？(y/n)
set /p choice="请输入选择: "

if /i "%choice%"=="y" (
    echo.
    echo 开始生成时间矩阵数据...
    echo 注意：这个过程可能需要很长时间，请耐心等待
    echo.
    curl -X POST "http://localhost:8084/legacy-travel-time/generate"
    echo.
    echo 生成完成！
) else (
    echo 已取消生成操作
)

echo.
echo 测试脚本执行完成
pause
