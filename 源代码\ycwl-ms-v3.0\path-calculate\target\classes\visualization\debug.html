<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>算法调试可视化平台</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" />
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }
        
        .app-container {
            display: flex;
            height: 100vh;
            background: white;
            border-radius: 12px;
            margin: 8px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        #map {
            flex: 1;
            height: 100%;
            position: relative;
        }
        
        .sidebar {
            width: 450px;
            background: white;
            display: flex;
            flex-direction: column;
            border-left: 1px solid #e0e6ed;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }
        
        .header h1 {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .header .subtitle {
            font-size: 12px;
            opacity: 0.9;
        }

        .stage-selector {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .stage-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
        }

        .stage-btn {
            padding: 12px 16px;
            border: 2px solid #e2e8f0;
            background: white;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            font-weight: 600;
            text-align: center;
        }

        .stage-btn:hover {
            border-color: #667eea;
            background: #f7faff;
        }

        .stage-btn.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: transparent;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .stage-btn .stage-icon {
            font-size: 16px;
            margin-bottom: 5px;
            display: block;
        }

        .folder-selector {
            margin-bottom: 20px;
        }

        .folder-upload-area {
            border: 2px dashed #cbd5e0;
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
            margin-top: 10px;
        }

        .folder-upload-area:hover, .folder-upload-area.dragover {
            border-color: #667eea;
            background: #f7faff;
            transform: translateY(-2px);
        }

        .folder-upload-area.loaded {
            border-color: #22c55e;
            background: #f0fdf4;
        }

        .folder-text {
            font-size: 14px;
            font-weight: 600;
            color: #374151;
        }

        .folder-info {
            font-size: 12px;
            color: #64748b;
        }

        .session-selector {
            margin-top: 15px;
        }

        .session-label {
            font-weight: 600;
            margin-bottom: 8px;
            color: #374151;
            font-size: 14px;
        }

        .session-select {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: white;
            font-size: 13px;
            transition: border-color 0.3s ease;
        }

        .session-select:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .content-area {
            flex: 1;
            overflow-y: auto;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .stage-info {
            padding: 20px;
            background: #f8fafc;
            border-bottom: 1px solid #e2e8f0;
        }

        .stage-title {
            font-size: 16px;
            font-weight: 700;
            color: #1e293b;
            margin-bottom: 10px;
        }

        .stage-description {
            font-size: 14px;
            color: #64748b;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .stage-metrics {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 15px;
        }

        .metric-card {
            background: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            border: 1px solid #e2e8f0;
        }

        .metric-value {
            font-size: 20px;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 11px;
            color: #64748b;
        }

        .stage-content {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            min-height: 0;
        }

        .depot-groups {
            margin-bottom: 20px;
            max-height: calc(100vh - 400px);
            overflow-y: auto;
        }

        .depot-group {
            margin-bottom: 20px;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            overflow: hidden;
            background: white;
        }

        .depot-header {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            padding: 15px 20px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-weight: 600;
        }

        .depot-info {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .depot-icon {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .expand-icon {
            transition: transform 0.3s ease;
        }

        .depot-group.expanded .expand-icon {
            transform: rotate(180deg);
        }

        .routes-list {
            display: none;
        }

        .depot-group.expanded .routes-list {
            display: block;
        }

        .route-item {
            padding: 15px 20px;
            border-bottom: 1px solid #f1f5f9;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .route-item:hover {
            background: #f8fafc;
        }

        .route-item.selected {
            background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            border-left: 4px solid #3b82f6;
        }

        .route-name {
            font-weight: 600;
            color: #1e293b;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .route-stats {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 10px;
            font-size: 12px;
        }

        .route-stat {
            text-align: center;
            padding: 8px;
            background: #f8fafc;
            border-radius: 6px;
        }

        .route-stat-value {
            font-weight: 700;
            color: #3b82f6;
        }

        .route-stat-label {
            color: #64748b;
            font-size: 10px;
            margin-top: 2px;
        }

        .playback-controls {
            position: absolute;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: white;
            padding: 15px 20px;
            border-radius: 12px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.15);
            border: 1px solid #e2e8f0;
            display: none;
        }

        .playback-controls.active {
            display: block;
        }

        .playback-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .playback-title {
            font-weight: 600;
            color: #1e293b;
        }

        .playback-buttons {
            display: flex;
            gap: 8px;
        }

        .playback-btn {
            width: 40px;
            height: 40px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .playback-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .playback-progress {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .progress-slider {
            flex: 1;
            height: 6px;
            border-radius: 3px;
            background: #e2e8f0;
            appearance: none;
            outline: none;
        }

        .progress-slider::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .progress-text {
            font-size: 12px;
            color: #64748b;
            min-width: 120px;
            text-align: right;
        }

        .legend {
            background: #f8fafc;
            padding: 20px;
            border-top: 1px solid #e2e8f0;
        }

        .legend-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: #374151;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            font-size: 13px;
        }

        .legend-marker {
            width: 14px;
            height: 14px;
            margin-right: 12px;
            border-radius: 4px;
            border: 2px solid white;
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        .legend-line {
            width: 24px;
            height: 4px;
            margin-right: 12px;
            border-radius: 2px;
        }

        .loading {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.95);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 2000;
            -webkit-backdrop-filter: blur(5px);
            backdrop-filter: blur(5px);
        }

        .loading-content {
            text-align: center;
            padding: 40px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f4f6;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            padding: 15px;
            background: #fef2f2;
            color: #dc2626;
            border-left: 4px solid #ef4444;
            border-radius: 8px;
            margin: 20px;
            font-size: 14px;
        }

        .step-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 12px 20px;
            border-radius: 25px;
            font-size: 14px;
            font-weight: 600;
            z-index: 1000;
            -webkit-backdrop-filter: blur(10px);
            backdrop-filter: blur(10px);
            display: none;
        }

        .step-indicator.active {
            display: block;
        }

        /* 聚类阶段特殊样式 */
        .cluster-colors {
            --cluster-1: #ef4444;
            --cluster-2: #3b82f6;
            --cluster-3: #059669;
            --cluster-4: #d97706;
            --cluster-5: #7c3aed;
            --cluster-6: #db2777;
            --cluster-7: #0891b2;
            --cluster-8: #65a30d;
            --cluster-9: #dc2626;
            --cluster-10: #1d4ed8;
        }

        /* 时间均衡阶段样式 */
        .balance-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 600;
            margin-left: 8px;
        }

        .balance-excellent { background: #dcfce7; color: #166534; }
        .balance-good { background: #fef3c7; color: #92400e; }
        .balance-poor { background: #fecaca; color: #991b1b; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .sidebar {
                width: 400px;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
                margin: 4px;
            }
            
            .sidebar {
                width: 100%;
                height: 50%;
            }
            
            #map {
                height: 50%;
            }
        }

        /* 自定义滚动条 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(135deg, #5a67d8, #6b46c1);
        }
    </style>
</head>
<body>
    <div class="loading" id="loading" style="display: none;">
        <div class="loading-content">
            <div class="spinner"></div>
            <div>正在加载调试数据...</div>
        </div>
    </div>
    
    <div class="app-container">
        <div id="map">
            <div class="step-indicator" id="stepIndicator">
                <i class="fas fa-play"></i>
                <span id="stepText">准备阶段</span>
            </div>
        </div>
        
        <div class="sidebar">
            <div class="header">
                <h1><i class="fas fa-bug"></i> 算法调试可视化</h1>
                <div class="subtitle">Algorithm Debug Visualization Platform</div>
            </div>
            
            <div class="stage-selector">
                <div class="folder-selector">
                    <div class="session-label">选择调试文件夹</div>
                    <div class="folder-upload-area" id="folderUploadArea" onclick="document.getElementById('folderInput').click()">
                        <div style="margin-bottom: 10px;">
                            <i class="fas fa-folder-open" style="font-size: 32px; color: #667eea;"></i>
                        </div>
                        <div class="folder-text" id="folderText">点击选择调试文件夹</div>
                        <div class="folder-info" id="folderInfo" style="margin-top: 10px; font-size: 12px; color: #64748b;"></div>
                    </div>
                    <input type="file" id="folderInput" webkitdirectory directory multiple style="display: none;" />
                </div>
                
                <div class="stage-buttons" id="stageButtons" style="display: none;">
                    <button class="stage-btn active" data-stage="clustering">
                        <span class="stage-icon"><i class="fas fa-layer-group"></i></span>
                        聚类阶段
                    </button>
                    <button class="stage-btn" data-stage="convex_hull">
                        <span class="stage-icon"><i class="fas fa-draw-polygon"></i></span>
                        凸包计算
                    </button>
                    <button class="stage-btn" data-stage="tsp">
                        <span class="stage-icon"><i class="fas fa-route"></i></span>
                        TSP优化
                    </button>
                    <button class="stage-btn" data-stage="time_balance">
                        <span class="stage-icon"><i class="fas fa-balance-scale"></i></span>
                        时间均衡
                    </button>
                    <button class="stage-btn" data-stage="final">
                        <span class="stage-icon"><i class="fas fa-check-circle"></i></span>
                        最终结果
                    </button>
                    <button class="stage-btn" data-stage="summary">
                        <span class="stage-icon"><i class="fas fa-chart-line"></i></span>
                        总结分析
                    </button>
                </div>
                
                <div class="session-selector" id="sessionSelector" style="display: none;">
                    <div class="session-label">选择调试会话</div>
                    <select id="sessionSelect" class="session-select">
                        <option value="">请选择调试会话...</option>
                    </select>
                </div>
            </div>

            <div class="content-area">
                <div class="stage-info" id="stageInfo">
                    <div class="stage-title" id="stageTitle">选择调试文件夹</div>
                    <div class="stage-description" id="stageDescription">点击上方文件夹图标，选择包含调试JSON文件的文件夹来开始分析</div>
                    <div class="stage-metrics" id="stageMetrics">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <div class="stage-content" id="stageContent">
                    <div class="error-message" style="display: none;" id="errorMessage">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span id="errorText"></span>
                    </div>
                    
                    <div class="depot-groups" id="depotGroups">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>

            <!-- <div class="legend" id="legend">
                <div class="legend-title">图例说明</div>
                <div id="legendContent">
                    
                </div>
            </div> -->
        </div>
    </div>
    
    <div class="playback-controls" id="playbackControls">
        <div class="playback-header">
            <div class="playback-title" id="playbackTitle">聚类过程回放</div>
            <div class="playback-buttons">
                <button class="playback-btn" id="playBtn" onclick="togglePlayback()">
                    <i class="fas fa-play" id="playIcon"></i>
                </button>
                <button class="playback-btn" onclick="previousStep()">
                    <i class="fas fa-step-backward"></i>
                </button>
                <button class="playback-btn" onclick="nextStep()">
                    <i class="fas fa-step-forward"></i>
                </button>
                <button class="playback-btn" onclick="resetPlayback()">
                    <i class="fas fa-undo"></i>
                </button>
            </div>
        </div>
        <div class="playback-progress">
            <input type="range" class="progress-slider" id="progressSlider" min="0" max="100" value="0">
            <div class="progress-text" id="progressText">步骤 0 / 0</div>
        </div>
    </div>

    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
    <script>
        // 全局变量
        let map;
        let debugData = {};
        let currentStage = 'clustering';
        let currentSession = null;
        let playbackTimer = null;
        let currentStep = 0;
        let maxSteps = 0;
        let isPlaying = false;
        let loadedFiles = {};
        
        // 图层组
        let layerGroups = {
            clusters: L.layerGroup(),
            routes: L.layerGroup(),
            convexHulls: L.layerGroup(),
            accumulations: L.layerGroup(),
            depots: L.layerGroup()
        };

        // 颜色方案
        const clusterColors = [
            '#ef4444', '#3b82f6', '#059669', '#d97706', '#7c3aed',
            '#db2777', '#0891b2', '#65a30d', '#dc2626', '#1d4ed8'
        ];

        const depotColors = {
            1: '#ef4444', 2: '#3b82f6', 3: '#059669', 4: '#d97706',
            5: '#7c3aed', 6: '#db2777', 7: '#0891b2', 8: '#65a30d'
        };

        // 初始化地图
        function initMap() {
            map = L.map('map', {
                zoomControl: false
            }).setView([24.5, 113.5], 10);
            
            L.control.zoom({
                position: 'bottomright'
            }).addTo(map);
            
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18
            }).addTo(map);

            Object.values(layerGroups).forEach(group => group.addTo(map));
        }

        // 处理文件夹选择
        function handleFolderSelect(files) {
            if (!files || files.length === 0) return;
            
            document.getElementById('loading').style.display = 'flex';
            loadedFiles = {};
            
            try {
                // 分析文件并按会话分组
                const sessions = {};
                let fileCount = 0;
                
                Array.from(files).forEach(file => {
                    if (file.name.endsWith('.json')) {
                        fileCount++;
                        const reader = new FileReader();
                        
                        reader.onload = function(e) {
                            try {
                                const data = JSON.parse(e.target.result);
                                
                                // 从文件名提取会话ID和阶段
                                const fileName = file.name;
                                let sessionId = '';
                                let stage = '';
                                
                                if (fileName.includes('session_summary_debug_')) {
                                    sessionId = fileName.match(/debug_(\d{8}_\d{6})/)?.[0] || '';
                                    stage = 'session_summary';
                                } else {
                                    const match = fileName.match(/(.+)_results_debug_(\d{8}_\d{6})\.json/);
                                    if (match) {
                                        stage = match[1];
                                        sessionId = `debug_${match[2]}`;
                                    }
                                }
                                
                                if (sessionId && stage) {
                                    if (!sessions[sessionId]) {
                                        sessions[sessionId] = {};
                                    }
                                    sessions[sessionId][stage] = data;
                                }
                                
                                // 检查是否所有文件都已加载
                                fileCount--;
                                if (fileCount === 0) {
                                    loadedFiles = sessions;
                                    populateSessionSelector();
                                    document.getElementById('loading').style.display = 'none';
                                    
                                    // 更新UI状态
                                    const folderArea = document.getElementById('folderUploadArea');
                                    const folderText = document.getElementById('folderText');
                                    const folderInfo = document.getElementById('folderInfo');
                                    
                                    folderArea.classList.add('loaded');
                                    folderText.textContent = '调试文件已加载';
                                    folderInfo.innerHTML = `
                                        <i class="fas fa-check-circle" style="color: #22c55e;"></i>
                                        发现 ${Object.keys(sessions).length} 个调试会话，共 ${Array.from(files).length} 个文件
                                    `;
                                    
                                    // 显示阶段按钮和会话选择器
                                    document.getElementById('stageButtons').style.display = 'grid';
                                    document.getElementById('sessionSelector').style.display = 'block';
                                }
                                
                            } catch (error) {
                                console.error(`解析文件 ${file.name} 失败:`, error);
                                fileCount--;
                                if (fileCount === 0) {
                                    document.getElementById('loading').style.display = 'none';
                                    showError('部分文件解析失败');
                                }
                            }
                        };
                        
                        reader.onerror = function() {
                            console.error(`读取文件 ${file.name} 失败`);
                            fileCount--;
                            if (fileCount === 0) {
                                document.getElementById('loading').style.display = 'none';
                                showError('文件读取失败');
                            }
                        };
                        
                        reader.readAsText(file);
                    }
                });
                
                if (fileCount === 0) {
                    document.getElementById('loading').style.display = 'none';
                    showError('未找到有效的JSON调试文件');
                }
                
            } catch (error) {
                document.getElementById('loading').style.display = 'none';
                showError(`处理文件夹失败: ${error.message}`);
            }
        }

        // 填充会话选择器
        function populateSessionSelector() {
            const sessionSelect = document.getElementById('sessionSelect');
            const sessions = Object.keys(loadedFiles).sort().reverse(); // 按时间倒序
            
            sessionSelect.innerHTML = '<option value="">请选择调试会话...</option>' +
                sessions.map(sessionId => {
                    const displayName = sessionId.replace('debug_', '会话 ');
                    const date = sessionId.match(/debug_(\d{8}_\d{6})/)?.[1];
                    if (date) {
                        const formatted = date.replace(/(\d{4})(\d{2})(\d{2})_(\d{2})(\d{2})(\d{2})/, '$1-$2-$3 $4:$5:$6');
                        return `<option value="${sessionId}">${formatted}</option>`;
                    }
                    return `<option value="${sessionId}">${displayName}</option>`;
                }).join('');
        }

        // 加载调试数据（从已加载的文件中）
        function loadDebugData(sessionId) {
            if (!sessionId || !loadedFiles[sessionId]) {
                debugData = {};
                currentSession = null;
                return;
            }
            
            debugData = { ...loadedFiles[sessionId], sessionId };
            currentSession = sessionId;
            
            hideError();
            switchStage(currentStage);
            updateStageInfo();
            
            // 显示成功消息
            const folderInfo = document.getElementById('folderInfo');
            const stageCount = Object.keys(debugData).filter(key => key !== 'sessionId').length;
            folderInfo.innerHTML = `
                <i class="fas fa-check-circle" style="color: #22c55e;"></i>
                已加载会话 ${sessionId.replace('debug_', '')}，包含 ${stageCount} 个阶段数据
            `;
        }

        // 显示错误信息
        function showError(message) {
            const errorDiv = document.getElementById('errorMessage');
            const errorText = document.getElementById('errorText');
            errorText.textContent = message;
            errorDiv.style.display = 'block';
        }

        // 隐藏错误信息
        function hideError() {
            document.getElementById('errorMessage').style.display = 'none';
        }

        // 切换阶段
        function switchStage(stage) {
            // 更新按钮状态
            document.querySelectorAll('.stage-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            document.querySelector(`[data-stage="${stage}"]`).classList.add('active');
            
            currentStage = stage;
            updateStageInfo();
            renderStage();
        }

        // 更新阶段信息
        function updateStageInfo() {
            if (!currentSession || !debugData[currentStage]) {
                return;
            }

            const stageTitle = document.getElementById('stageTitle');
            const stageDescription = document.getElementById('stageDescription');
            const stageMetrics = document.getElementById('stageMetrics');

            const stageInfo = getStageInfo(currentStage);
            stageTitle.textContent = stageInfo.title;
            stageDescription.textContent = stageInfo.description;
            
            // 生成指标卡片
            const metrics = getStageMetrics(currentStage);
            stageMetrics.innerHTML = metrics.map(metric => `
                <div class="metric-card">
                    <div class="metric-value">${metric.value}</div>
                    <div class="metric-label">${metric.label}</div>
                </div>
            `).join('');
        }

        // 获取阶段信息
        function getStageInfo(stage) {
            const info = {
                clustering: {
                    title: '聚类阶段',
                    description: '将聚集区分配到不同的路线中，每个路线对应一个聚类'
                },
                convex_hull: {
                    title: '凸包计算',
                    description: '计算每条路线服务区域的凸包，用于面积和形状分析'
                },
                tsp: {
                    title: 'TSP优化',
                    description: '优化每条路线内聚集区的访问顺序，最小化行驶时间'
                },
                time_balance: {
                    title: '时间均衡',
                    description: '平衡各路线的工作时间，优化整体配送效率'
                },
                final: {
                    title: '最终结果',
                    description: '展示经过所有优化步骤后的最终路线规划结果'
                },
                summary: {
                    title: '执行总结',
                    description: '分析整个算法的执行时间和性能指标'
                }
            };
            return info[stage] || { title: '未知阶段', description: '' };
        }

        // 获取阶段指标
        function getStageMetrics(stage) {
            if (!debugData[currentStage]) return [];

            switch (stage) {
                case 'clustering':
                    return getClusteringMetrics();
                case 'convex_hull':
                    return getConvexHullMetrics();
                case 'tsp':
                    return getTSPMetrics();
                case 'time_balance':
                    return getTimeBalanceMetrics();
                case 'final':
                    return getFinalMetrics();
                case 'summary':
                    return getSummaryMetrics();
                default:
                    return [];
            }
        }

        // 聚类阶段指标
        function getClusteringMetrics() {
            const data = debugData.clustering;
            if (!data || !data.results) return [];

            let totalClusters = 0;
            let totalAccumulations = 0;
            let depotCount = 0;

            Object.values(data.results).forEach(depot => {
                depotCount++;
                totalClusters += depot.totalClusters || 0;
                if (depot.clusters) {
                    Object.values(depot.clusters).forEach(cluster => {
                        totalAccumulations += cluster.accumulationCount || 0;
                    });
                }
            });

            return [
                { value: depotCount, label: '中转站数' },
                { value: totalClusters, label: '聚类数' },
                { value: totalAccumulations, label: '聚集区数' }
            ];
        }

        // TSP阶段指标
        function getTSPMetrics() {
            const data = debugData.tsp;
            if (!data || !data.results) return [];

            let totalRoutes = 0;
            let totalTime = 0;
            let totalAccumulations = 0;

            Object.values(data.results).forEach(depot => {
                if (depot.routes) {
                    Object.values(depot.routes).forEach(route => {
                        totalRoutes++;
                        totalTime += route.totalWorkTime || 0;
                        totalAccumulations += route.accumulationCount || 0;
                    });
                }
            });

            return [
                { value: totalRoutes, label: '路线数' },
                { value: Math.round(totalTime), label: '总时间(分)' },
                { value: totalAccumulations, label: '聚集区数' }
            ];
        }

        // 时间均衡指标
        function getTimeBalanceMetrics() {
            const data = debugData.time_balance;
            if (!data || !data.results) return [];

            let totalRoutes = 0;
            let totalTime = 0;
            let minTime = Infinity;
            let maxTime = 0;

            Object.values(data.results).forEach(depot => {
                if (depot.routes) {
                    Object.values(depot.routes).forEach(route => {
                        totalRoutes++;
                        const time = route.finalWorkTime || 0;
                        totalTime += time;
                        minTime = Math.min(minTime, time);
                        maxTime = Math.max(maxTime, time);
                    });
                }
            });

            const timeVariance = totalRoutes > 0 ? Math.round(maxTime - minTime) : 0;

            return [
                { value: totalRoutes, label: '路线数' },
                { value: Math.round(totalTime / totalRoutes), label: '平均时间(分)' },
                { value: timeVariance, label: '时间差(分)' }
            ];
        }

        // 最终结果指标
        function getFinalMetrics() {
            const data = debugData.final;
            if (!data || !data.routes) return [];

            const totalRoutes = data.routes.length;
            const totalAccumulations = data.routes.reduce((sum, route) => sum + (route.accumulationCount || 0), 0);
            const avgEfficiency = data.routes.reduce((sum, route) => sum + (route.workEfficiency || 0), 0) / totalRoutes;

            return [
                { value: totalRoutes, label: '路线数' },
                { value: totalAccumulations, label: '聚集区数' },
                { value: `${(avgEfficiency * 100).toFixed(1)}%`, label: '平均效率' }
            ];
        }

        // 执行总结指标
        function getSummaryMetrics() {
            const data = debugData.session_summary;
            if (!data) return [];

            const totalTime = Math.round(data.totalExecutionTime / 1000);
            const stageCount = Object.keys(data.stageExecutionTimes || {}).length;

            return [
                { value: `${totalTime}s`, label: '总执行时间' },
                { value: stageCount, label: '执行阶段数' },
                { value: data.sessionId?.split('_').pop() || 'N/A', label: '会话ID' }
            ];
        }

        // 其他指标方法
        function getConvexHullMetrics() {
            // 实现凸包指标
            return [
                { value: 'N/A', label: '凸包数' },
                { value: 'N/A', label: '总面积' },
                { value: 'N/A', label: '平均面积' }
            ];
        }

        // 渲染当前阶段
        function renderStage() {
            // 清除所有图层
            Object.values(layerGroups).forEach(group => group.clearLayers());
            hideError();

            if (!currentSession || !debugData[currentStage]) {
                showError('没有可用的调试数据');
                return;
            }

            switch (currentStage) {
                case 'clustering':
                    renderClustering();
                    break;
                case 'convex_hull':
                    renderConvexHull();
                    break;
                case 'tsp':
                    renderTSP();
                    break;
                case 'time_balance':
                    renderTimeBalance();
                    break;
                case 'final':
                    renderFinal();
                    break;
                case 'summary':
                    renderSummary();
                    break;
            }

            generateDepotGroups();
            updateLegend();
        }

        // 渲染聚类阶段
        function renderClustering() {
            const data = debugData.clustering;
            if (!data || !data.results) return;

            let globalClusterIndex = 0; // 全局聚类索引，确保每个聚类颜色唯一
            
            Object.values(data.results).forEach(depot => {
                if (!depot.clusters) return;
                
                Object.values(depot.clusters).forEach(cluster => {
                    const color = clusterColors[globalClusterIndex % clusterColors.length];
                    
                    cluster.accumulations?.forEach(acc => {
                        const marker = L.circleMarker([acc.latitude, acc.longitude], {
                            radius: 8,
                            fillColor: color,
                            color: 'white',
                            weight: 2,
                            opacity: 1,
                            fillOpacity: 0.8,
                            clusterId: globalClusterIndex,
                            depotId: depot.transitDepotId,
                            stepIndex: globalClusterIndex // 用于回放控制
                        }).addTo(layerGroups.clusters);

                        marker.bindPopup(`
                            <div style="font-family: sans-serif;">
                                <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                    <i class="fas fa-layer-group"></i> ${acc.name}
                                </h4>
                                <p><strong>聚类:</strong> ${globalClusterIndex + 1}</p>
                                <p><strong>中转站:</strong> ${depot.transitDepotId} - ${depot.transitDepotName || ''}</p>
                                <p><strong>配送时间:</strong> ${acc.deliveryTime} 分钟</p>
                                <p><strong>坐标:</strong> ${acc.latitude.toFixed(6)}, ${acc.longitude.toFixed(6)}</p>
                            </div>
                        `);
                    });
                    
                    globalClusterIndex++;
                });
            });

            // 启用回放控制
            document.getElementById('playbackControls').classList.add('active');
            document.getElementById('playbackTitle').textContent = '聚类过程回放';
            maxSteps = globalClusterIndex;
            document.getElementById('progressSlider').max = maxSteps;
            updateProgressText();
            
            // 初始化回放状态
            currentStep = maxSteps; // 显示所有聚类
            updateClusteringStep();
        }

        // 更新聚类显示步骤
        function updateClusteringStep() {
            map.eachLayer(layer => {
                if (layer.options && layer.options.stepIndex !== undefined) {
                    if (layer.options.stepIndex < currentStep) {
                        // 显示这个聚类
                        layer.setStyle({
                            fillOpacity: 0.8,
                            opacity: 1
                        });
                    } else {
                        // 隐藏这个聚类
                        layer.setStyle({
                            fillOpacity: 0.1,
                            opacity: 0.3
                        });
                    }
                }
            });
        }

        // 渲染凸包阶段
        function renderConvexHull() {
            const data = debugData.convex_hull;
            if (!data || !data.results) return;

            Object.values(data.results).forEach(depot => {
                if (!depot.routes) return;
                
                Object.values(depot.routes).forEach((route, routeIndex) => {
                    const color = depotColors[depot.transitDepotId] || clusterColors[routeIndex % clusterColors.length];
                    
                    // 渲染凸包
                    if (route.convexHull && route.convexHull.length > 0) {
                        const hullCoords = route.convexHull.map(point => [point.latitude, point.longitude]);
                        
                        const polygon = L.polygon(hullCoords, {
                            color: color,
                            weight: 3,
                            fillColor: color,
                            fillOpacity: 0.2,
                            opacity: 0.8,
                            routeId: route.routeId || routeIndex,
                            depotId: depot.transitDepotId
                        }).addTo(layerGroups.convexHulls);

                        polygon.bindPopup(`
                            <div style="font-family: sans-serif;">
                                <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                    <i class="fas fa-draw-polygon"></i> ${route.routeName}
                                </h4>
                                <p><strong>凸包面积:</strong> ${route.convexHullArea?.toFixed(6) || 'N/A'} 平方度</p>
                                <p><strong>边界点数:</strong> ${route.convexHull.length}</p>
                                <p><strong>中转站:</strong> ${depot.transitDepotId}</p>
                                <p><strong>聚集区数:</strong> ${route.accumulationCount || 'N/A'}</p>
                            </div>
                        `);
                    }

                    // 渲染聚集区点
                    if (route.accumulations) {
                        route.accumulations.forEach(acc => {
                            const marker = L.circleMarker([acc.latitude, acc.longitude], {
                                radius: 6,
                                fillColor: color,
                                color: 'white',
                                weight: 2,
                                opacity: 1,
                                fillOpacity: 0.7,
                                routeId: route.routeId || routeIndex,
                                depotId: depot.transitDepotId
                            }).addTo(layerGroups.accumulations);
                        });
                    }
                });
            });

            // 禁用回放控制
            document.getElementById('playbackControls').classList.remove('active');
        }

        // 渲染TSP阶段
        function renderTSP() {
            const data = debugData.tsp;
            if (!data || !data.results) return;

            Object.values(data.results).forEach(depot => {
                if (!depot.routes) return;
                
                Object.values(depot.routes).forEach((route, routeIndex) => {
                    const color = depotColors[depot.transitDepotId] || clusterColors[routeIndex % clusterColors.length];
                    
                    // 渲染路线序列（如果有坐标信息）
                    if (route.accumulationSequence && route.accumulationSequence.length > 0) {
                        // 由于TSP数据可能没有具体坐标，我们创建一个概念性的展示
                        const sequenceText = route.accumulationSequence.slice(0, 5).join(' → ') + 
                                           (route.accumulationSequence.length > 5 ? ' ...' : '');
                        
                        // 在地图中心显示路线信息（临时解决方案）
                        const centerLat = 24.5 + (routeIndex * 0.1);
                        const centerLng = 113.5 + (depot.transitDepotId * 0.1);
                        
                        const marker = L.circleMarker([centerLat, centerLng], {
                            radius: 12,
                            fillColor: color,
                            color: 'white',
                            weight: 3,
                            opacity: 1,
                            fillOpacity: 0.8,
                            routeId: route.routeId || routeIndex,
                            depotId: depot.transitDepotId
                        }).addTo(layerGroups.routes);

                        marker.bindPopup(`
                            <div style="font-family: sans-serif;">
                                <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                    <i class="fas fa-route"></i> ${route.routeName}
                                </h4>
                                <p><strong>聚集区数:</strong> ${route.accumulationCount}</p>
                                <p><strong>工作时间:</strong> ${Math.round(route.totalWorkTime)} 分钟</p>
                                <p><strong>访问序列:</strong></p>
                                <div style="font-size: 11px; max-height: 100px; overflow-y: auto; background: #f8f9fa; padding: 8px; border-radius: 4px;">
                                    ${route.accumulationSequence.join(' → ')}
                                </div>
                            </div>
                        `);
                    }
                });
            });

            // 禁用回放控制
            document.getElementById('playbackControls').classList.remove('active');
        }

        // 渲染时间均衡阶段
        function renderTimeBalance() {
            const data = debugData.time_balance;
            if (!data || !data.results) return;

            const allTimes = [];
            Object.values(data.results).forEach(depot => {
                if (depot.routes) {
                    Object.values(depot.routes).forEach(route => {
                        if (route.finalWorkTime) {
                            allTimes.push(route.finalWorkTime);
                        }
                    });
                }
            });

            const minTime = Math.min(...allTimes);
            const maxTime = Math.max(...allTimes);

            Object.values(data.results).forEach(depot => {
                if (!depot.routes) return;
                
                Object.values(depot.routes).forEach((route, routeIndex) => {
                    const workTime = route.finalWorkTime || 0;
                    const normalizedTime = (workTime - minTime) / (maxTime - minTime);
                    
                    // 根据时间均衡程度选择颜色
                    let color;
                    if (normalizedTime < 0.33) {
                        color = '#22c55e'; // 绿色 - 时间短
                    } else if (normalizedTime < 0.67) {
                        color = '#eab308'; // 黄色 - 时间中等
                    } else {
                        color = '#ef4444'; // 红色 - 时间长
                    }
                    
                    const centerLat = 24.5 + (routeIndex * 0.1);
                    const centerLng = 113.5 + (depot.transitDepotId * 0.1);
                    
                    // 圆圈大小反映工作时间长短
                    const radius = 8 + (normalizedTime * 12);
                    
                    const marker = L.circleMarker([centerLat, centerLng], {
                        radius: radius,
                        fillColor: color,
                        color: 'white',
                        weight: 3,
                        opacity: 1,
                        fillOpacity: 0.8,
                        routeId: route.routeId || routeIndex,
                        depotId: depot.transitDepotId
                    }).addTo(layerGroups.routes);

                    const balanceGrade = getBalanceGrade(workTime);
                    marker.bindPopup(`
                        <div style="font-family: sans-serif;">
                            <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                <i class="fas fa-balance-scale"></i> ${route.routeName}
                            </h4>
                            <p><strong>最终工作时间:</strong> ${Math.round(workTime)} 分钟</p>
                            <p><strong>聚集区数:</strong> ${route.accumulationCount}</p>
                            <p><strong>时间均衡等级:</strong> 
                                <span class="balance-indicator balance-${balanceGrade.class}">${balanceGrade.text}</span>
                            </p>
                            <p><strong>相对时间位置:</strong> ${(normalizedTime * 100).toFixed(1)}%</p>
                        </div>
                    `);
                });
            });

            // 禁用回放控制
            document.getElementById('playbackControls').classList.remove('active');
        }

        // 渲染最终结果
        function renderFinal() {
            const data = debugData.final;
            if (!data || !data.routes) return;

            // 使用与主可视化页面相同的渲染逻辑
            data.routes.forEach((route, index) => {
                const color = depotColors[route.transitDepotId] || '#3b82f6';
                
                if (route.polyline && route.polyline.length > 0) {
                    const polylineCoords = route.polyline.map(point => [point.latitude, point.longitude]);
                    
                    const polyline = L.polyline(polylineCoords, {
                        color: color,
                        weight: 4,
                        opacity: 0.8,
                        routeId: route.routeId,
                        depotId: route.transitDepotId
                    }).addTo(layerGroups.routes);

                    polyline.bindPopup(`
                        <div style="font-family: sans-serif;">
                            <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                                <i class="fas fa-route"></i> ${route.routeName}
                            </h4>
                            <p><strong>聚集区数:</strong> ${route.accumulationCount}</p>
                            <p><strong>工作时间:</strong> ${route.totalWorkTime.toFixed(1)} 分钟</p>
                            <p><strong>效率:</strong> ${(route.workEfficiency * 100).toFixed(1)}%</p>
                        </div>
                    `);
                }

                // 渲染聚集区
                if (route.polyline) {
                    route.polyline.forEach((point, pointIndex) => {
                        if (pointIndex > 0 && pointIndex < route.polyline.length - 1) {
                            const marker = L.circleMarker([point.latitude, point.longitude], {
                                radius: 6,
                                fillColor: color,
                                color: 'white',
                                weight: 2,
                                opacity: 1,
                                fillOpacity: 0.7,
                                routeId: route.routeId,
                                depotId: route.transitDepotId
                            }).addTo(layerGroups.accumulations);
                        }
                    });
                }
            });
        }

        // 渲染执行总结
        function renderSummary() {
            const data = debugData.session_summary;
            if (!data || !data.stageExecutionTimes) return;

            const totalTime = data.totalExecutionTime;
            const stages = Object.entries(data.stageExecutionTimes);
            
            // 在地图上创建执行时间可视化
            const centerLat = 24.5;
            const centerLng = 113.5;
            const radius = 0.5; // 半径范围
            
            stages.forEach(([stageName, stageTime], index) => {
                const percentage = (stageTime / totalTime) * 100;
                const angle = (index / stages.length) * 2 * Math.PI;
                
                // 根据执行时间比例计算位置
                const lat = centerLat + Math.cos(angle) * radius * (percentage / 100);
                const lng = centerLng + Math.sin(angle) * radius * (percentage / 100);
                
                // 根据执行时间选择颜色和大小
                let color = '#3b82f6';
                if (percentage > 50) color = '#ef4444';
                else if (percentage > 25) color = '#f59e0b';
                else if (percentage > 10) color = '#10b981';
                
                const markerRadius = Math.max(8, Math.min(25, percentage * 0.8));
                
                const marker = L.circleMarker([lat, lng], {
                    radius: markerRadius,
                    fillColor: color,
                    color: 'white',
                    weight: 3,
                    opacity: 1,
                    fillOpacity: 0.7,
                    stageName: stageName
                }).addTo(layerGroups.routes);

                marker.bindPopup(`
                    <div style="font-family: sans-serif;">
                        <h4 style="margin: 0 0 10px 0; color: #1e293b;">
                            <i class="fas fa-clock"></i> ${getStageInfo(stageName).title}
                        </h4>
                        <p><strong>执行时间:</strong> ${Math.round(stageTime / 1000)} 秒</p>
                        <p><strong>时间占比:</strong> ${percentage.toFixed(2)}%</p>
                        <p><strong>毫秒数:</strong> ${stageTime.toLocaleString()} ms</p>
                        <div style="margin-top: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; font-size: 12px;">
                            ${getStageInfo(stageName).description}
                        </div>
                    </div>
                `);
            });

            // 在中心显示总执行时间
            const totalMarker = L.circleMarker([centerLat, centerLng], {
                radius: 30,
                fillColor: '#667eea',
                color: 'white',
                weight: 4,
                opacity: 1,
                fillOpacity: 0.9
            }).addTo(layerGroups.routes);

            totalMarker.bindPopup(`
                <div style="font-family: sans-serif; text-align: center;">
                    <h4 style="margin: 0 0 15px 0; color: #1e293b;">
                        <i class="fas fa-chart-line"></i> 总执行统计
                    </h4>
                    <div style="font-size: 24px; font-weight: bold; color: #667eea; margin-bottom: 10px;">
                        ${Math.round(totalTime / 1000)}s
                    </div>
                    <p><strong>会话ID:</strong> ${data.sessionId}</p>
                    <p><strong>执行阶段:</strong> ${stages.length} 个</p>
                    <p><strong>时间戳:</strong> ${new Date(data.timestamp).toLocaleString()}</p>
                    <div style="margin-top: 15px; padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 12px;">
                        <strong>各阶段详情:</strong><br>
                        ${stages.map(([name, time]) => 
                            `${getStageInfo(name).title}: ${Math.round(time/1000)}s`
                        ).join('<br>')}
                    </div>
                </div>
            `);

            // 禁用回放控制
            document.getElementById('playbackControls').classList.remove('active');
        }

        // 生成中转站分组
        function generateDepotGroups() {
            const depotGroups = document.getElementById('depotGroups');
            
            if (!currentSession || !debugData[currentStage]) {
                depotGroups.innerHTML = '<div class="error-message">没有可用的数据</div>';
                return;
            }

            let groupsHTML = '';
            
            switch (currentStage) {
                case 'clustering':
                    groupsHTML = generateClusteringGroups();
                    break;
                case 'tsp':
                    groupsHTML = generateTSPGroups();
                    break;
                case 'time_balance':
                    groupsHTML = generateTimeBalanceGroups();
                    break;
                case 'final':
                    groupsHTML = generateFinalGroups();
                    break;
                case 'summary':
                    groupsHTML = generateSummaryGroups();
                    break;
                default:
                    groupsHTML = '<div>此阶段暂无详细数据</div>';
            }
            
            depotGroups.innerHTML = groupsHTML;
        }

        // 生成聚类阶段分组
        function generateClusteringGroups() {
            const data = debugData.clustering;
            if (!data || !data.results) return '';

            let globalClusterIndex = 0; // 用于计算全局聚类索引

            return Object.entries(data.results).map(([depotKey, depot]) => {
                const depotColor = depotColors[depot.transitDepotId] || '#3b82f6';
                
                const clustersHTML = Object.entries(depot.clusters || {}).map(([clusterKey, cluster], localIndex) => {
                    // 使用全局聚类索引
                    const currentGlobalIndex = globalClusterIndex + localIndex;
                    const clusterColor = clusterColors[currentGlobalIndex % clusterColors.length];
                    
                    return `
                        <div class="route-item" onclick="highlightCluster(${currentGlobalIndex}, ${depot.transitDepotId})">
                            <div style="display: flex; align-items: center; gap: 10px;">
                                <div style="width: 12px; height: 12px; background: ${clusterColor}; border-radius: 50%;"></div>
                                <div class="route-name">聚类 ${currentGlobalIndex + 1}</div>
                            </div>
                            <div class="route-stats">
                                <div class="route-stat">
                                    <div class="route-stat-value">${cluster.accumulationCount}</div>
                                    <div class="route-stat-label">聚集区</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${Math.round(cluster.totalWorkTime)}</div>
                                    <div class="route-stat-label">工作时间</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${cluster.accumulations?.length || 0}</div>
                                    <div class="route-stat-label">点位数</div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');
                
                // 更新全局索引，准备处理下一个depot
                globalClusterIndex += Object.keys(depot.clusters || {}).length;

                return `
                    <div class="depot-group expanded">
                        <div class="depot-header" onclick="toggleDepotGroup('${depotKey}')">
                            <div class="depot-info">
                                <div class="depot-icon" style="background: ${depotColor};"></div>
                                <div>
                                    <div style="font-weight: 700;">${depot.transitDepotName || `中转站 ${depot.transitDepotId}`}</div>
                                    <div style="font-size: 12px; color: #64748b;">${depot.totalClusters} 个聚类</div>
                                </div>
                            </div>
                            <div class="expand-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="routes-list">
                            ${clustersHTML}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 生成TSP阶段分组
        function generateTSPGroups() {
            const data = debugData.tsp;
            if (!data || !data.results) return '';

            return Object.entries(data.results).map(([depotKey, depot]) => {
                const depotColor = depotColors[depot.transitDepotId] || '#3b82f6';
                
                const routesHTML = Object.entries(depot.routes || {}).map(([routeKey, route]) => {
                    return `
                        <div class="route-item">
                            <div class="route-name">${route.routeName}</div>
                            <div class="route-stats">
                                <div class="route-stat">
                                    <div class="route-stat-value">${route.accumulationCount}</div>
                                    <div class="route-stat-label">聚集区</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${Math.round(route.totalWorkTime)}</div>
                                    <div class="route-stat-label">工作时间</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${route.accumulationSequence?.length || 0}</div>
                                    <div class="route-stat-label">访问点</div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                return `
                    <div class="depot-group">
                        <div class="depot-header" onclick="toggleDepotGroup('${depotKey}')">
                            <div class="depot-info">
                                <div class="depot-icon" style="background: ${depotColor};"></div>
                                <div>
                                    <div style="font-weight: 700;">中转站 ${depot.transitDepotId}</div>
                                    <div style="font-size: 12px; color: #64748b;">${depot.totalRoutes} 条路线</div>
                                </div>
                            </div>
                            <div class="expand-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="routes-list">
                            ${routesHTML}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 生成时间均衡分组
        function generateTimeBalanceGroups() {
            const data = debugData.time_balance;
            if (!data || !data.results) return '';

            return Object.entries(data.results).map(([depotKey, depot]) => {
                const depotColor = depotColors[depot.transitDepotId] || '#3b82f6';
                
                const routesHTML = Object.entries(depot.routes || {}).map(([routeKey, route]) => {
                    const balanceGrade = getBalanceGrade(route.finalWorkTime);
                    
                    return `
                        <div class="route-item">
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div class="route-name">${route.routeName}</div>
                                <span class="balance-indicator balance-${balanceGrade.class}">${balanceGrade.text}</span>
                            </div>
                            <div class="route-stats">
                                <div class="route-stat">
                                    <div class="route-stat-value">${route.accumulationCount}</div>
                                    <div class="route-stat-label">聚集区</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${Math.round(route.finalWorkTime)}</div>
                                    <div class="route-stat-label">最终时间</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">-</div>
                                    <div class="route-stat-label">调整量</div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                return `
                    <div class="depot-group">
                        <div class="depot-header" onclick="toggleDepotGroup('${depotKey}')">
                            <div class="depot-info">
                                <div class="depot-icon" style="background: ${depotColor};"></div>
                                <div>
                                    <div style="font-weight: 700;">中转站 ${depot.transitDepotId}</div>
                                    <div style="font-size: 12px; color: #64748b;">${depot.totalRoutes} 条路线</div>
                                </div>
                            </div>
                            <div class="expand-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="routes-list">
                            ${routesHTML}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 生成最终结果分组
        function generateFinalGroups() {
            const data = debugData.final;
            if (!data || !data.routes) return '';

            const routesByDepot = {};
            data.routes.forEach(route => {
                if (!routesByDepot[route.transitDepotId]) {
                    routesByDepot[route.transitDepotId] = [];
                }
                routesByDepot[route.transitDepotId].push(route);
            });

            return Object.entries(routesByDepot).map(([depotId, routes]) => {
                const depotColor = depotColors[depotId] || '#3b82f6';
                
                const routesHTML = routes.map(route => {
                    return `
                        <div class="route-item" onclick="highlightRoute(${route.routeId})">
                            <div class="route-name">${route.routeName}</div>
                            <div class="route-stats">
                                <div class="route-stat">
                                    <div class="route-stat-value">${route.accumulationCount}</div>
                                    <div class="route-stat-label">聚集区</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${Math.round(route.totalWorkTime)}</div>
                                    <div class="route-stat-label">工作时间</div>
                                </div>
                                <div class="route-stat">
                                    <div class="route-stat-value">${(route.workEfficiency * 100).toFixed(1)}%</div>
                                    <div class="route-stat-label">效率</div>
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                return `
                    <div class="depot-group">
                        <div class="depot-header" onclick="toggleDepotGroup('depot_${depotId}')">
                            <div class="depot-info">
                                <div class="depot-icon" style="background: ${depotColor};"></div>
                                <div>
                                    <div style="font-weight: 700;">中转站 ${depotId}</div>
                                    <div style="font-size: 12px; color: #64748b;">${routes.length} 条路线</div>
                                </div>
                            </div>
                            <div class="expand-icon">
                                <i class="fas fa-chevron-down"></i>
                            </div>
                        </div>
                        <div class="routes-list">
                            ${routesHTML}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 生成执行总结分组
        function generateSummaryGroups() {
            const data = debugData.session_summary;
            if (!data) return '';

            const stageStats = Object.entries(data.stageExecutionTimes || {}).map(([stage, time]) => {
                const percentage = ((time / data.totalExecutionTime) * 100).toFixed(1);
                return `
                    <div class="route-item">
                        <div class="route-name">${getStageInfo(stage).title}</div>
                        <div class="route-stats">
                            <div class="route-stat">
                                <div class="route-stat-value">${Math.round(time / 1000)}s</div>
                                <div class="route-stat-label">执行时间</div>
                            </div>
                            <div class="route-stat">
                                <div class="route-stat-value">${percentage}%</div>
                                <div class="route-stat-label">时间占比</div>
                            </div>
                            <div class="route-stat">
                                <div class="route-stat-value">-</div>
                                <div class="route-stat-label">状态</div>
                            </div>
                        </div>
                    </div>
                `;
            }).join('');

            return `
                <div class="depot-group expanded">
                    <div class="depot-header">
                        <div class="depot-info">
                            <div class="depot-icon" style="background: #667eea;"></div>
                            <div>
                                <div style="font-weight: 700;">执行阶段统计</div>
                                <div style="font-size: 12px; color: #64748b;">总执行时间: ${Math.round(data.totalExecutionTime / 1000)}秒</div>
                            </div>
                        </div>
                    </div>
                    <div class="routes-list">
                        ${stageStats}
                    </div>
                </div>
            `;
        }

        // 更新图例
        function updateLegend() {
            const legendContent = document.getElementById('legendContent');
            let legendHTML = '';

            switch (currentStage) {
                case 'clustering':
                    // 只显示实际使用的聚类颜色
                    if (debugData.clustering && debugData.clustering.results) {
                        let totalClusters = 0;
                        Object.values(debugData.clustering.results).forEach(depot => {
                            totalClusters += Object.keys(depot.clusters || {}).length;
                        });
                        
                        for (let i = 0; i < Math.min(totalClusters, clusterColors.length); i++) {
                            legendHTML += `
                                <div class="legend-item">
                                    <div class="legend-marker" style="background: ${clusterColors[i]};"></div>
                                    <span>聚类 ${i + 1}</span>
                                </div>
                            `;
                        }
                    } else {
                        legendHTML = '<div class="legend-item">暂无聚类数据</div>';
                    }
                    break;
                    
                case 'convex_hull':
                    legendHTML += `
                        <div class="legend-item">
                            <div class="legend-marker" style="background: rgba(59, 130, 246, 0.2); border: 2px solid #3b82f6;"></div>
                            <span>凸包区域</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #3b82f6;"></div>
                            <span>聚集区点</span>
                        </div>
                    `;
                    break;
                    
                case 'tsp':
                    if (debugData.tsp && debugData.tsp.results) {
                        const depotIds = Object.values(debugData.tsp.results).map(depot => depot.transitDepotId || 'N/A');
                        const uniqueDepots = [...new Set(depotIds)];
                        
                        uniqueDepots.forEach(depotId => {
                            const color = depotColors[depotId] || '#3b82f6';
                            legendHTML += `
                                <div class="legend-item">
                                    <div class="legend-marker" style="background: ${color};"></div>
                                    <span>中转站 ${depotId} 路线</span>
                                </div>
                            `;
                        });
                    }
                    break;
                    
                case 'time_balance':
                    legendHTML += `
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #22c55e;"></div>
                            <span>时间短 (0-33%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #eab308;"></div>
                            <span>时间中等 (33-67%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #ef4444;"></div>
                            <span>时间长 (67-100%)</span>
                        </div>
                    `;
                    break;
                    
                case 'final':
                    if (debugData.final && debugData.final.routes) {
                        const depotIds = [...new Set(debugData.final.routes.map(route => route.transitDepotId))];
                        depotIds.forEach(depotId => {
                            const color = depotColors[depotId] || '#3b82f6';
                            legendHTML += `
                                <div class="legend-item">
                                    <div class="legend-marker" style="background: ${color};"></div>
                                    <span>中转站 ${depotId}</span>
                                </div>
                            `;
                        });
                    }
                    break;
                    
                case 'summary':
                    legendHTML += `
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #ef4444;"></div>
                            <span>高耗时阶段 (>50%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #f59e0b;"></div>
                            <span>中耗时阶段 (25-50%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #10b981;"></div>
                            <span>低耗时阶段 (10-25%)</span>
                        </div>
                        <div class="legend-item">
                            <div class="legend-marker" style="background: #3b82f6;"></div>
                            <span>极低耗时阶段 (<10%)</span>
                        </div>
                    `;
                    break;
                    
                default:
                    legendHTML = '<div class="legend-item">暂无图例</div>';
            }

            legendContent.innerHTML = legendHTML;
        }

        // 工具函数
        function getTimeBalanceColor(workTime) {
            // 根据工作时间返回颜色
            if (workTime < 300) return '#22c55e';
            if (workTime < 600) return '#eab308';
            return '#ef4444';
        }

        function getBalanceGrade(workTime) {
            if (workTime < 300) return { class: 'excellent', text: '优秀' };
            if (workTime < 600) return { class: 'good', text: '良好' };
            return { class: 'poor', text: '需优化' };
        }

        // 交互函数
        function toggleDepotGroup(depotKey) {
            const group = document.querySelector(`[onclick="toggleDepotGroup('${depotKey}')"]`).parentElement;
            group.classList.toggle('expanded');
        }

        function highlightCluster(clusterIndex, depotId) {
            // 高亮指定聚类
            map.eachLayer(layer => {
                if (layer.options && layer.options.clusterId === clusterIndex && layer.options.depotId === depotId) {
                    layer.setStyle({
                        radius: 12,
                        weight: 4,
                        fillOpacity: 1
                    });
                } else if (layer.options && layer.options.clusterId !== undefined) {
                    layer.setStyle({
                        radius: 6,
                        weight: 2,
                        fillOpacity: 0.5
                    });
                }
            });
        }

        function highlightRoute(routeId) {
            // 高亮指定路线
            map.eachLayer(layer => {
                if (layer.options && layer.options.routeId === routeId) {
                    layer.setStyle({
                        weight: 6,
                        opacity: 1
                    });
                } else if (layer.options && layer.options.routeId !== undefined) {
                    layer.setStyle({
                        weight: 2,
                        opacity: 0.5
                    });
                }
            });
        }

        // 回放控制
        function togglePlayback() {
            if (isPlaying) {
                pausePlayback();
            } else {
                startPlayback();
            }
        }

        function startPlayback() {
            isPlaying = true;
            document.getElementById('playIcon').className = 'fas fa-pause';
            
            playbackTimer = setInterval(() => {
                if (currentStep < maxSteps) {
                    currentStep++;
                    updatePlaybackStep();
                } else {
                    pausePlayback();
                }
            }, 1000);
        }

        function pausePlayback() {
            isPlaying = false;
            document.getElementById('playIcon').className = 'fas fa-play';
            if (playbackTimer) {
                clearInterval(playbackTimer);
            }
        }

        function previousStep() {
            if (currentStep > 0) {
                currentStep--;
                updatePlaybackStep();
            }
        }

        function nextStep() {
            if (currentStep < maxSteps) {
                currentStep++;
                updatePlaybackStep();
            }
        }

        function resetPlayback() {
            currentStep = 0;
            pausePlayback();
            updatePlaybackStep();
        }

        function updatePlaybackStep() {
            document.getElementById('progressSlider').value = currentStep;
            updateProgressText();
            updateStepIndicator();
            
            // 根据当前阶段和步骤更新地图显示
            switch (currentStage) {
                case 'clustering':
                    updateClusteringStep();
                    break;
                default:
                    // 其他阶段暂不支持步骤控制
                    break;
            }
        }

        function updateProgressText() {
            document.getElementById('progressText').textContent = `步骤 ${currentStep} / ${maxSteps}`;
        }

        function updateStepIndicator() {
            const indicator = document.getElementById('stepIndicator');
            const stepText = document.getElementById('stepText');
            
            indicator.classList.add('active');
            stepText.textContent = `${getStageInfo(currentStage).title} - 步骤 ${currentStep}`;
        }

        // 初始化
        function init() {
            initMap();

            // 绑定文件夹选择事件
            const folderInput = document.getElementById('folderInput');
            const folderUploadArea = document.getElementById('folderUploadArea');

            folderInput.addEventListener('change', function(e) {
                handleFolderSelect(e.target.files);
            });

            // 拖拽支持
            folderUploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                folderUploadArea.classList.add('dragover');
            });

            folderUploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                folderUploadArea.classList.remove('dragover');
            });

            folderUploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                folderUploadArea.classList.remove('dragover');
                
                // 检查是否为文件夹拖拽
                const items = e.dataTransfer.items;
                if (items) {
                    const files = [];
                    for (let i = 0; i < items.length; i++) {
                        if (items[i].kind === 'file') {
                            const file = items[i].getAsFile();
                            if (file.name.endsWith('.json')) {
                                files.push(file);
                            }
                        }
                    }
                    if (files.length > 0) {
                        handleFolderSelect(files);
                    }
                } else {
                    const files = Array.from(e.dataTransfer.files).filter(file => file.name.endsWith('.json'));
                    if (files.length > 0) {
                        handleFolderSelect(files);
                    }
                }
            });

            // 绑定阶段切换事件
            document.querySelectorAll('.stage-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    switchStage(btn.dataset.stage);
                });
            });

            // 绑定会话选择事件
            document.getElementById('sessionSelect').addEventListener('change', function() {
                loadDebugData(this.value);
            });

            // 绑定回放控制事件
            document.getElementById('progressSlider').addEventListener('input', function() {
                currentStep = parseInt(this.value);
                updatePlaybackStep();
            });
        }

        // 启动应用
        init();
    </script>
</body>
</html>