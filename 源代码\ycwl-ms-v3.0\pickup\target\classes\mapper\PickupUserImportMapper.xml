<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pickup.mapper.PickupUserImportMapper">

    <update id="updateByCustomerCode">
        update pickup_user_import set pickup_type=#{pickupType} where customer_code=#{customerCode}
    </update>

    <update id="updateAllFieldsByCustomerCode">
        update pickup_user_import set 
            contact_name=#{contactName},
            customer_manager_name=#{customerManagerName},
            store_address=#{storeAddress},
            pickup_type=#{pickupType}
        where customer_code=#{customerCode}
    </update>

    <update id="updatePickupType">
        UPDATE pickup_user
            INNER JOIN pickup_user_import
        ON pickup_user.customer_code = pickup_user_import.customer_code
            SET pickup_user.type = pickup_user_import.pickup_type;
    </update>

    <select id="selectByCustomerCode" resultType="com.ict.ycwl.pickup.pojo.entity.PickupUserImport">
        select * from pickup_user_import where customer_code=#{customerCode}
    </select>
</mapper>