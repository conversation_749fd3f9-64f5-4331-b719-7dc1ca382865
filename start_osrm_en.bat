@echo off
chcp 65001 >nul
echo ========================================
echo Starting OSRM Service on port 5000
echo ========================================

echo 1. Checking Docker...
docker --version
if %errorlevel% neq 0 (
    echo ERROR: Docker not installed or not running
    echo Please install Docker Desktop first
    pause
    exit /b 1
)

echo 2. Stopping old containers...
docker stop osrm-backend 2>nul
docker rm osrm-backend 2>nul

echo 3. Starting OSRM service with built-in data...
echo This will use OSRM container with built-in Berlin data for testing
echo OSRM service will start at http://localhost:5000

docker run -d --name osrm-backend -p 5000:5000 osrm/osrm-backend

echo 4. Waiting for service to start...
timeout /t 10 /nobreak >nul

echo 5. Testing service...
curl -s "http://localhost:5000/route/v1/driving/13.388860,52.517037;13.397634,52.529407?overview=false" >nul
if %errorlevel% equ 0 (
    echo.
    echo SUCCESS: OSRM service is running!
    echo Service URL: http://localhost:5000
    echo.
    echo Now you can restart your Java service and test!
    echo.
    echo To stop OSRM: docker stop osrm-backend
) else (
    echo.
    echo FAILED: OSRM service failed to start
    echo Check logs: docker logs osrm-backend
)

pause
