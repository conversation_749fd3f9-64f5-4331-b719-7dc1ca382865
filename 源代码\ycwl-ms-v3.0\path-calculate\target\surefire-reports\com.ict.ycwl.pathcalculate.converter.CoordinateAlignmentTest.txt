-------------------------------------------------------------------------------
Test set: com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest
-------------------------------------------------------------------------------
Tests run: 1, Failures: 0, Errors: 1, Skipped: 0, Time elapsed: 9.113 s <<< FAILURE! - in com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest
testCoordinateAlignment  Time elapsed: 0.001 s  <<< ERROR!
java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicDatasourceController': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"

