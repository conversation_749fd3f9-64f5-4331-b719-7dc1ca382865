# 粤北卷烟物流运输规划数据库说明文档

## 数据库概述

**数据库名称：** ycdb  
**数据库类型：** MySQL 9.0.1  
**字符集：** utf8mb4  
**创建时间：** 2025年7月17日  
**用途：** 粤北卷烟物流运输规划业务系统

## 业务领域

这是一个针对粤北地区卷烟物流配送的综合管理系统，涵盖以下主要业务：

- 配送路线规划与优化
- 车辆调度管理
- 客户与店铺管理
- 配送域和聚集区管理
- 反馈问题跟踪
- 用户权限管理
- 取货户管理
- 系统参数配置

## 数据表详细说明

### 1. 核心业务表

#### 1.1 accumulation（聚集区表）
聚集区是配送路线中的重要节点，代表多个店铺的集中配送点。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| accumulation_id | bigint | 聚集区ID（主键） |
| leader_name | varchar(30) | 聚集区负责人名称 |
| accumulation_name | varchar(50) | 聚集区名称 |
| leader_phone | varchar(20) | 负责人联系电话 |
| longitude | double | 经度坐标 |
| latitude | double | 纬度坐标 |
| area_name | varchar(255) | 所属大区名称 |
| area_id | bigint | 所属大区ID |
| route_id | bigint | 关联路线ID |
| transit_depot_id | bigint | 所属中转站ID |
| accumulation_address | varchar(255) | 详细地址 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_delete | tinyint | 软删除标识（0:否，1:是） |

#### 1.2 store（店铺表）
记录所有需要配送的零售店铺信息。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| store_id | bigint | 店铺ID（主键） |
| customer_code | varchar(255) | 客户编码（唯一） |
| store_name | varchar(255) | 店铺名称 |
| store_address | varchar(255) | 店铺地址 |
| longitude | double | 经度坐标 |
| latitude | double | 纬度坐标 |
| type | varchar(20) | 商圈类型 |
| order_cycle | varchar(20) | 订货周期 |
| district | varchar(20) | 行政区 |
| area_name | varchar(20) | 所属大区 |
| area_id | bigint | 大区ID |
| contact_name | varchar(20) | 联系人姓名 |
| contact_phone | varchar(30) | 联系电话 |
| gear | varchar(255) | 客户档位 |
| location_type | char(1) | 位置类型（0:城区，1:乡镇） |
| route_id | bigint | 配送路线ID |
| route_name | varchar(30) | 路线名称 |
| accumulation_id | bigint | 所属聚集区ID |
| customer_manager_id | bigint | 客户专员ID |
| customer_manager_name | varchar(30) | 客户专员姓名 |
| group_id | bigint | 班组ID |
| is_special | varchar(255) | 是否特殊点（0:否，1:是） |
| special_type | varchar(255) | 特殊点类型 |
| remark | varchar(255) | 备注 |
| status | char(1) | 状态（0:异常，1:正常） |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_delete | tinyint | 软删除标识 |

#### 1.3 route（路线表）
配送路线信息，包含路线规划和版本管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| route_id | bigint | 路线ID（主键） |
| route_name | varchar(255) | 路线名称 |
| distance | varchar(255) | 路线距离（米） |
| transit_depot_id | bigint | 中转站ID |
| area_id | bigint | 大区ID |
| polyline | longtext | 路线坐标点串 |
| convex | longtext | 凸包坐标点串 |
| cargo_weight | varchar(255) | 载货量 |
| work_time | varchar(255) | 工作时长 |
| version_number | int | 版本号 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_delete | tinyint | 软删除标识 |

#### 1.4 car（车辆表）
配送车辆信息管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| car_id | bigint | 车辆ID（主键） |
| license_plate_number | char(10) | 车牌号 |
| max_load | varchar(30) | 最大载重（吨） |
| max_distance | varchar(50) | 最大行驶距离（米） |
| delivery_time | varchar(30) | 最长工作时长 |
| integral | varchar(30) | 积分 |
| actual_load | varchar(30) | 实际载货量 |
| actual_time | varchar(30) | 实际工作时间 |
| status | char(1) | 状态（0:异常，1:正常） |
| area_id | bigint | 所属大区ID |
| transit_depot_id | bigint | 中转站ID |
| car_driver_id | bigint | 驾驶员ID |
| delivery_area_id | int | 配送域ID |
| route_name | varchar(255) | 路线名称 |
| week | varchar(255) | 星期 |
| date | varchar(255) | 日期 |
| is_fact | varchar(255) | 是否实况（0:基本信息，1:实况） |
| is_delete | int | 软删除标识 |

### 2. 管理支撑表

#### 2.1 area（大区表）
行政区域划分管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| area_id | bigint | 大区ID（主键） |
| area_name | varchar(30) | 大区名称 |

#### 2.2 transit_depot（中转场表）
物流中转站点管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| transit_depot_id | bigint | 中转场ID（主键） |
| transit_depot_name | varchar(30) | 中转场名称 |
| longitude | varchar(255) | 经度坐标 |
| latitude | varchar(255) | 纬度坐标 |
| status | char(1) | 启用状态（0:禁用，1:启用） |
| area_id | bigint | 所属大区ID |
| group_id | bigint | 班组ID |
| is_delete | int | 软删除标识 |

#### 2.3 delivery_area（配送域表）
配送区域管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| delivery_area_id | bigint | 配送域ID（主键） |
| delivery_area_name | varchar(255) | 配送域名称 |
| team_id | bigint | 班组ID |
| area_id | bigint | 行政区ID |
| transit_depot_id | bigint | 中转站ID |
| delivery_type_id | bigint | 配送类型ID |
| route_number | int | 路径数量 |
| car_number | int | 车辆数量 |
| is_delete | int | 软删除标识 |

#### 2.4 team（班组表）
配送班组管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| team_id | bigint | 班组ID（主键） |
| team_name | varchar(255) | 班组名称 |
| delivery_area_name | varchar(255) | 配送域名称 |
| transit_depot_name | varchar(255) | 中转站名称 |
| car_sum | int | 车辆总数 |
| route_sum | int | 路线总数 |
| is_delete | int | 软删除标识 |

### 3. 用户与权限表

#### 3.1 user（用户表）
系统用户信息管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| user_id | bigint | 用户ID（主键） |
| login_name | varchar(30) | 登录账号 |
| user_name | varchar(30) | 用户姓名 |
| work_number | varchar(20) | 工号 |
| password | varchar(255) | 密码 |
| sex | varchar(4) | 性别 |
| position | varchar(20) | 职位 |
| department | varchar(20) | 部门 |
| phone | varchar(30) | 联系电话 |
| email | varchar(30) | 邮箱 |
| status | char(1) | 状态（0:禁用，1:启用） |
| avatar_path | varchar(255) | 头像路径 |
| role_id | bigint | 角色ID |
| rank | varchar(30) | 职级 |
| sign_time | date | 入职时间 |
| create_by | bigint | 创建者ID |
| create_time | datetime | 创建时间 |
| update_by | bigint | 更新者ID |
| update_time | datetime | 更新时间 |

#### 3.2 role（角色表）
系统角色管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| role_id | bigint | 角色ID（主键） |
| role_name | varchar(30) | 角色名称 |
| role_state | varchar(50) | 角色描述 |

#### 3.3 operation（权限点表）
系统权限点管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| operation_id | bigint | 权限点ID（主键） |
| operation_name | varchar(255) | 权限点名称 |
| operation_state | varchar(50) | 权限点描述 |

#### 3.4 role_operation（角色权限表）
角色与权限的关联关系。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| role_id | bigint | 角色ID |
| operation_id | bigint | 权限点ID |
| status | char(1) | 权限状态（0:未开启，1:开启） |

### 4. 反馈与问题跟踪表

#### 4.1 feedback（反馈表）
异常反馈信息管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| feedback_id | bigint | 反馈ID（主键） |
| customer_code | varchar(30) | 客户编码 |
| route_id | bigint | 路线ID |
| route_name | varchar(20) | 路线名称 |
| order_date | datetime | 订单日期 |
| delivery_work_number | varchar(30) | 送货员工号 |
| delivery_name | varchar(20) | 送货员姓名 |
| customer_manager_name | varchar(20) | 客户专员姓名 |
| manager_work_number | varchar(30) | 客户专员工号 |
| feedback_information | varchar(50) | 反馈异常信息 |
| feedback_type | char(1) | 反馈类型（1:物流反馈，2:营销反馈） |
| feedback_status | int | 处理状态（0:未处理，1:处理中，2:已处理，3:无需处理） |
| area_name | varchar(30) | 大区名称 |
| create_by | bigint | 创建者ID |
| create_time | datetime | 创建时间 |
| update_by | bigint | 更新者ID |
| update_time | datetime | 更新时间 |
| complete_time | datetime | 完成时间 |

#### 4.2 feedback_reply（反馈回复表）
反馈问题的回复信息。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| reply_id | bigint | 回复ID（主键） |
| feedback_id | bigint | 反馈ID |
| reply_content | varchar(255) | 回复内容 |
| reply_type | char(1) | 回复类型（1:送货部，2:营销部） |
| create_by | bigint | 创建者ID |
| create_time | datetime | 创建时间 |

### 5. 取货户管理表

#### 5.1 pickup_user（取货户表）
取货户信息管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 取货户ID（主键） |
| customer_code | varchar(255) | 客户编码 |
| contact_name | varchar(255) | 客户名称 |
| store_name | varchar(255) | 商店名称 |
| customer_manager_name | varchar(255) | 负责人 |
| contact_phone | varchar(255) | 订货电话 |
| store_address | varchar(255) | 商铺地址 |
| longitude | double | 经度坐标 |
| latitude | double | 纬度坐标 |
| road_grade | char(1) | 道路等级（0:城区，1:乡镇） |
| gear | varchar(255) | 档位 |
| delivery_distance | double | 配送距离 |
| pickup_containers | varchar(255) | 取货柜地址 |
| type | varchar(255) | 取货柜类型 |
| weights | double | 权值 |
| locks | int | 锁定状态（0:未锁定，1:锁定） |
| color | int | 分配状态（1:普通商户未分配，2:普通商户已分配，3:定点取货户未分配，4:定点取货户已分配） |
| accumulation_id | bigint | 聚集区ID |

#### 5.2 site_selection（选址表）
取货地选址管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 选址ID（主键） |
| pickup_name | varchar(255) | 取货地名称 |
| pickup_address | varchar(255) | 详细地址 |
| longitude | double | 经度坐标 |
| latitude | double | 纬度坐标 |
| type | varchar(255) | 取货地类型 |
| status | int | 状态（1:禁用，2:启用未分配，3:启用已分配） |
| city | varchar(255) | 市 |
| district | varchar(255) | 区/县 |
| town | varchar(255) | 镇 |
| village | varchar(255) | 村 |

### 6. 系统配置表

#### 6.1 system_parameter（系统参数表）
系统运行参数配置。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | int | 参数ID（主键） |
| accumulation_intensity | double | 聚集区密集度系数 |
| shore_unload_city_time | double | 城区商铺卸货时长（小时） |
| shore_unload_township_time | double | 乡村商铺卸货时长（小时） |
| freeway | double | 高速公路车辆时速（km/h） |
| urban_roads | double | 城区公路车辆时速（km/h） |
| township_roads | double | 乡镇公路车辆时速（km/h） |
| loading_time | double | 装车时长 |

#### 6.2 version（版本表）
系统版本管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| version_id | int | 版本ID（主键） |
| version_name | varchar(255) | 版本名称 |
| version_db | varchar(255) | 实际版本号 |
| version_info | varchar(255) | 版本备注 |
| update_time | datetime | 更新时间 |
| is_show | int | 是否显示（0:不显示，1:显示） |

### 7. 辅助与统计表

#### 7.1 point_distance（路段表）
路段距离信息管理。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| point_distance_id | bigint | 路段ID（主键） |
| origin | varchar(255) | 起始点 |
| destination | varchar(255) | 终点 |
| distance | varchar(255) | 路段距离 |
| polyline | longtext | 路段坐标点串 |
| type | varchar(255) | 路段类型（ordinary:普通，bridge:桥） |
| transit_depot_id | bigint | 中转站ID |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |
| is_delete | tinyint | 软删除标识 |

#### 7.2 route_detail（路线详情表）
路线详细统计信息。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 详情ID（主键） |
| route_id | bigint | 路线ID |
| accumulation_count | int | 聚集区数量 |
| city_count | int | 城区商铺数量 |
| country_count | int | 乡镇商铺数量 |
| loading_time | varchar(30) | 装车时长 |
| transit_time | varchar(30) | 途中时长 |
| delivery_time | varchar(30) | 卸货配送时长 |
| total_time | varchar(30) | 总时长 |
| freeewat_dist | double | 高速公路行驶里程（米） |
| uraban_roads_dist | double | 城区公路行驶里程（米） |
| township_roads_dist | double | 乡镇公路行驶里程（米） |
| second_transit_time | double | 二次中转站时长（分钟） |

#### 7.3 file_import_logs（文件导入日志表）
文件导入操作记录。

| 字段名 | 类型 | 说明 |
|--------|------|------|
| file_id | int | 文件ID（主键） |
| file_name | varchar(255) | 文件名 |
| file_size | varchar(255) | 文件大小（KB） |
| import_time | varchar(255) | 导入时间 |
| user_name | varchar(255) | 操作用户 |
| status | varchar(255) | 导入状态 |
| store_or_car | varchar(255) | 文件类型（0:商铺表，1:车辆实况表） |

## 存储过程

### 1. CalculateDeliveryDistance
**功能：** 计算取货户到聚集区的配送距离  
**算法：** 使用Haversine公式计算球面距离  
**参数：** 无  
**返回：** 更新的记录数

### 2. setLocationType
**功能：** 根据商铺位置设置地理类型（城区/乡镇）  
**算法：** 基于中心点距离计算  
**参数：** 无  
**返回：** 无

## 数据库触发器

### 1. operation表触发器
- `after_insert_operation`: 新增权限点时自动为所有角色创建权限记录
- `delete_operation`: 删除权限点时自动删除相关角色权限记录

### 2. role表触发器
- `after_role_insert`: 新增角色时自动为该角色创建所有权限点记录
- `before_role_delete`: 删除角色前自动删除相关权限记录

### 3. version表触发器
- `update_updateTime_before_update`: 更新版本记录时自动更新时间戳

## 主要外键关系

1. **用户与角色：** user.role_id → role.role_id
2. **店铺与大区：** store.area_id → area.area_id
3. **店铺与聚集区：** store.accumulation_id → accumulation.accumulation_id
4. **路线与中转站：** route.transit_depot_id → transit_depot.transit_depot_id
5. **车辆与中转站：** car.transit_depot_id → transit_depot.transit_depot_id
6. **反馈与用户：** feedback.create_by → user.user_id

## 索引优化

主要索引包括：
- 客户编码唯一索引
- 经纬度组合索引
- 起点终点唯一索引
- 外键索引
- 时间戳索引

## 数据完整性约束

1. **主键约束：** 所有表都定义了主键
2. **外键约束：** 关键关联表设置了外键约束
3. **唯一约束：** 客户编码、用户登录名等设置唯一约束
4. **检查约束：** 状态字段限制取值范围
5. **非空约束：** 关键字段设置NOT NULL约束

## 软删除机制

大部分业务表采用软删除机制：
- 使用`is_delete`字段标记删除状态
- 0表示正常，1表示已删除
- 查询时需要添加`is_delete = 0`条件

## 时间戳管理

统一的时间戳管理：
- `create_time`：创建时间，默认CURRENT_TIMESTAMP
- `update_time`：更新时间，自动更新ON UPDATE CURRENT_TIMESTAMP

## 字符集与排序规则

- 主要使用utf8mb3和utf8mb4字符集
- 支持中文字符存储
- 部分字段使用latin1字符集存储英文数据

## 业务规则说明

1. **路线规划：** 支持多版本管理，包含坐标点串和凸包信息
2. **配送距离计算：** 使用地理坐标精确计算配送距离
3. **权限管理：** 基于角色的权限控制，支持细粒度权限配置
4. **反馈处理：** 完整的问题反馈与回复流程
5. **文件导入：** 支持批量数据导入，记录导入日志
6. **版本控制：** 路线和系统版本的完整版本管理

---

**文档版本：** 1.0  
**最后更新：** 2025年1月17日  
**维护人员：** 系统管理员 