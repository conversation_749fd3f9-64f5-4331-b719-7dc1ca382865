# travel_time表数据修复指南

## 🎯 目标
将travel_time表的覆盖度从当前的42.90%提升到80%以上，使`/calculateAll`接口能够正常工作。

## 📊 问题分析
- **当前状态**: 290,800条记录，覆盖度42.90%
- **目标状态**: 需要达到542,243条记录（80%覆盖度）
- **算法要求**: 每个中转站内部（中转站+聚集区）的全连接时间矩阵

## 🔧 修复方案

### 方案概述
使用SQL脚本直接在数据库中生成缺失的travel_time记录，采用简化的距离估算算法：
- **基础时间**: 2-5分钟
- **距离时间**: 基于坐标差值的简化计算
- **修正系数**: 考虑城市道路因素

### 执行步骤

#### 第一步：执行基础修复脚本
```bash
mysql -h localhost -P 3307 -u root -p"aA13717028793#" ycdb < travel_time_repair_final.sql
```

**预期结果**: 
- 完成新丰县中转站的数据修复
- 覆盖度提升到约50-55%

#### 第二步：执行扩展修复脚本
```bash
mysql -h localhost -P 3307 -u root -p"aA13717028793#" ycdb < travel_time_repair_part2.sql
```

**预期结果**:
- 完成所有中转站的基础连接
- 覆盖度提升到70-85%

#### 第三步：验证修复结果
```sql
-- 检查总体覆盖度
SELECT 
    COUNT(*) as total_records,
    677804 as expected_total,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as coverage_percent
FROM travel_time;

-- 如果覆盖度 >= 80%，则修复成功
```

#### 第四步：测试算法接口
```bash
curl -X GET "http://localhost:8084/original-route/calculateAll?apiKey=a123fae9da370c45984c58720bf3ac7c"
```

## 📈 各中转站数据需求

| 中转站ID | 中转站名称 | 聚集区数 | 需要点对数 | 优先级 |
|---------|-----------|---------|-----------|--------|
| 1 | 新丰县中转站 | 118 | 14,042 | 高 |
| 3 | 翁源县中转站 | 168 | 28,392 | 高 |
| 2 | 坪石镇中转站 | 237 | 56,406 | 中 |
| 4 | 马市烟叶工作站 | 327 | 107,256 | 中 |
| 5 | 班组一物流配送中心 | 497 | 247,506 | 低 |
| 6 | 班组二物流配送中心 | 473 | 224,202 | 低 |

## ⚠️ 注意事项

### 执行前检查
1. **数据库连接**: 确保能连接到MySQL数据库
2. **权限验证**: 确保有INSERT权限
3. **空间检查**: 确保数据库有足够空间（预计增加300-400MB）

### 执行过程监控
1. **分批执行**: 脚本已设置LIMIT限制，避免超时
2. **进度监控**: 每个步骤都有覆盖度检查SQL
3. **错误处理**: 使用NOT EXISTS避免重复插入

### 性能优化
1. **索引优化**: 执行前确保travel_time表有适当索引
2. **批量提交**: 脚本使用批量INSERT提高效率
3. **内存监控**: 大批量操作时监控数据库内存使用

## 🚨 故障排除

### 常见问题

#### 1. 执行超时
**症状**: SQL执行时间过长
**解决**: 减少LIMIT数值，分更多批次执行

#### 2. 内存不足
**症状**: MySQL报内存错误
**解决**: 重启MySQL服务，增加innodb_buffer_pool_size

#### 3. 重复键错误
**症状**: Duplicate entry错误
**解决**: 脚本已使用NOT EXISTS避免，如仍出现可忽略

#### 4. 坐标转换错误
**症状**: 数据类型转换失败
**解决**: 检查accumulation表中的坐标数据完整性

### 验证修复效果

#### 检查覆盖度
```sql
SELECT 
    COUNT(*) as records,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as coverage
FROM travel_time;
```

#### 检查数据质量
```sql
-- 检查是否有异常的时间值
SELECT COUNT(*) FROM travel_time 
WHERE travel_time <= 0 OR travel_time > 300;

-- 检查坐标格式
SELECT COUNT(*) FROM travel_time 
WHERE longitude_start = '' OR latitude_start = '';
```

#### 测试算法接口
```bash
# 测试覆盖度检查
curl "http://localhost:8084/original-route/check-algorithm-coverage"

# 测试完整算法
curl "http://localhost:8084/original-route/calculateAll?apiKey=a123fae9da370c45984c58720bf3ac7c"
```

## ✅ 成功标准

1. **覆盖度达标**: travel_time表覆盖度 ≥ 80%
2. **数据质量**: 无异常时间值（≤0或>300分钟）
3. **接口正常**: `/calculateAll`接口能正常返回结果
4. **算法验证**: PathPlanningUtilsTest测试通过

## 📞 技术支持

如果在执行过程中遇到问题：
1. 检查MySQL错误日志
2. 验证数据库连接和权限
3. 确认基础数据（accumulation、transit_depot表）的完整性
4. 如需回滚，可以删除新增的travel_time记录后重新执行
