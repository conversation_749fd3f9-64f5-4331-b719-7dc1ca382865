package com.ict.ycwl.pathcalculate;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import com.ict.ycwl.pathcalculate.pojo.TravelTime;
import com.ict.ycwl.pathcalculate.mapper.*;
import com.ict.ycwl.pathcalculate.pojo.*;
import com.ict.ycwl.pathcalculate.v6.OSRMTool;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;

import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;


/*
 * 目标： 1.要求班组内的每条路径之间的工作时长少于等于30分钟
 *      2.要求每个班组之间的工作时长差异小于30分钟
 *      3.要求生成的凸包不重叠或者少量重叠（大量重叠不能显示在页面上）
 * */


/*
 * 每条路径工作时长的由来：
 *       1. 工作时长有三部分组成，1.装货时长（查数据库） 2.运输时长  3.卸货时长
 * */

@Component
@ConditionalOnProperty(name = "legacy.travel-time.enabled", havingValue = "true", matchIfMissing = false)
public class RouteTest001 {

    @Autowired
    private RouteMapper routeMapper;

    @Autowired
    private SystemParameterMapper systemParameterMapper;

    @Autowired
    private AccumulationMapper accumulationMapper;

    @Autowired
    private StoreMapper storeMapper;

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private TransitDepotMapper transitDepotMapper;


    /*
     * 打印每个路径路径的路径坐标串，每条路径的凸包，每条路径的工作时长（分钟）
     * */
    public void test01() {
        List<Route> routes = routeMapper.selectList(new LambdaQueryWrapper<Route>().eq(Route::getIsDelete, 0));
        for (Route route : routes) {
            System.out.println("路径：" + route.getPolyline());
            System.out.println("凸包：" + route.getConvex());
            System.out.println("工作时长(分钟)：" + route.getWorkTime());
        }
    }

    /*
     * 查询工作时长相关参数
     * */
    public void Test02() {
        SystemParameter sp = systemParameterMapper.selectById(1);
        System.out.println(sp);
    }



    /*
     * 输入打卡点经纬度判断类型
     * */
    public void test04() {
        String storeLongitude = "";
        String storeLatitude = "";
        Store store = storeMapper.selectOne(new LambdaQueryWrapper<Store>().eq(Store::getLongitude, storeLongitude).eq(Store::getLatitude, storeLatitude).last("limit 1"));
        String locationType = store.getLocationType();
        if ("1".equals(locationType)) {
            //这个商铺是乡镇类型
        } else {
            //这个商铺是城镇类型
        }
    }



    /*
     * 前一个打卡点到后一个打卡点的距离
     * */
    public double[] saveDistanceInformation(DoublePoint point1, DoublePoint point2, String apiKey) throws ApiKeyException {
        String url = "https://restapi.amap.com/v3/direction/driving";
        apiKey = "3729e38b382749ba3a10bae7539e0d9a";
        // 拼接经度和纬度字符串
        String origin = point1.getPoint()[0] + "," + point1.getPoint()[1];
        String destination = point2.getPoint()[0] + "," + point2.getPoint()[1];

        // 构建请求URL
        String requestUrl = url + "?origin=" + origin + "&destination=" + destination + "&number=FD08088&extensions=all&output=json&key=" + apiKey;

        double dist = 0.0;
        double tolls = 0;
        JsonNode rootNode = null;
        for (int i = 0; i < 3; i++) {
            try {
                // 发送HTTP请求
                URL urlObj = new URL(requestUrl);
                HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
                connection.setRequestMethod("GET");

                // 读取响应数据
                Scanner scanner = new Scanner(connection.getInputStream());
                StringBuilder response = new StringBuilder();
                while (scanner.hasNextLine()) {
                    response.append(scanner.nextLine());
                }
                scanner.close();
                ObjectMapper objectMapper = new ObjectMapper();
                rootNode = objectMapper.readTree(response.toString());
                System.out.println(rootNode);
                //            double dist = rootNode.path("route").path("paths").get(0).path("distance").asDouble();
                JsonNode pathsNode = rootNode.path("route").path("paths");
                dist = pathsNode.get(0).path("distance").asDouble();
                tolls = pathsNode.get(0).path("tolls").asDouble();
                break;
            } catch (Exception e) {
                System.out.println("api异常s:" + e.getMessage());
            }
        }

        //dist是前一个打卡点到后一个打卡点的距离单位m，tolls大于0说明p1到p2走的是高速公路
        return new double[]{dist, tolls};//单位m
    }


    public void test08() throws ApiKeyException {
        DoublePoint p1 = new DoublePoint(new double[]{114.209602, 25.224353});
        DoublePoint p2 = new DoublePoint(new double[]{114.014315, 25.182799});
        double[] doubles = saveDistanceInformation(p1, p2, "");
        System.out.println(doubles[0]);
    }


    /*
     * 将所有配送域下的打卡点之间的卸货时长存在数据库中
     * */
    public void tese04() {
        //获取时长相关的参数
        SystemParameter sp = systemParameterMapper.selectById(1);
        //乡镇平均卸货时长(分钟)
        double shoreUnloadTownshipTime = sp.getShoreUnloadTownshipTime();
        //城镇平均卸货时长（分钟）
        double shoreUnloadCityTime = sp.getShoreUnloadCityTime();

        //查询出所有的配送域
        List<String> areas = jdbcTemplate.queryForList("select delivery_area_name from delivery_area where is_delete=0", String.class);

        //依次将每个大区下的打卡点的卸货时长存入数据库中
        for (String area : areas) {
            //查询当前配送域下所有的打卡点
            LambdaQueryWrapper<Accumulation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Accumulation::getAreaName, area).eq(Accumulation::getIsDelete, 0);
            List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);

            //依次计算单个打卡点的卸货时长
            for (Accumulation accumulation : accumulations) {

                //乡镇类型的商铺数量
                Long townshipCount = storeMapper.selectCount(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulation.getAccumulationId()).eq(Store::getIsDelete, 0).eq(Store::getLocationType, 1));

                //城镇类型的商铺数量
                Long townCount = storeMapper.selectCount(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulation.getAccumulationId()).eq(Store::getIsDelete, 0).eq(Store::getLocationType, 0));

                //统计总时长(分钟)
                double timeSum = 0;
                timeSum = townshipCount * shoreUnloadTownshipTime + townCount * shoreUnloadCityTime;

                String sql = "insert into unloading_time (acc_id,acclongitude,acclatitude,unloading_time) values(?,?,?,?)";
                //存入数据库中
                jdbcTemplate.update(sql, accumulation.getAccumulationId(), accumulation.getLongitude(), accumulation.getLatitude(), timeSum);
            }
        }
    }

    /*
     * 将所有配送域下的打卡点之间的行驶时长存在数据库中
     * */
    public void test06() throws ApiKeyException, JSONException {
        // 1. 定义额外坐标点列表（示例坐标，可根据需要修改）
        List<TransitDepot> transitDepots = transitDepotMapper.selectList(new LambdaQueryWrapper<TransitDepot>().eq(TransitDepot::getIsDelete, 0));
        ArrayList<Accumulation> accumulations1 = new ArrayList<>();
        for (TransitDepot transitDepot : transitDepots) {
            Accumulation accumulation = new Accumulation();
            accumulation.setLongitude(Double.valueOf(transitDepot.getLongitude()));
            accumulation.setLatitude(Double.valueOf(transitDepot.getLatitude()));
            accumulations1.add(accumulation);
        }


        List<Accumulation> extraPoints = accumulations1;

        // 获取时长相关的参数（原有逻辑）
        SystemParameter sp = systemParameterMapper.selectById(1);
        double TownshipDrivingSpeed = sp.getTownshipRoads();
        double TownDrivingSpeed = sp.getUrbanRoads();
        double highwaySpeed = sp.getFreeway();

        // 查询出所有的配送域（原有逻辑）
        List<String> areas = jdbcTemplate.queryForList(
                "select delivery_area_name from delivery_area where is_delete=0",
                String.class
        );
        areas.add("市辖区");
        final int MAX_COORDINATES_PER_BATCH = 500;

        for (String area : areas) {
            // 查询当前配送域下所有的打卡点（原有逻辑）
            LambdaQueryWrapper<Accumulation> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(Accumulation::getAreaName, area).eq(Accumulation::getIsDelete, 0);
            List<Accumulation> accumulations = accumulationMapper.selectList(queryWrapper);
            if (accumulations.isEmpty()) continue;

            // 合并当前配送域坐标和额外坐标
            List<Accumulation> allPoints = new ArrayList<>(accumulations);
            allPoints.addAll(extraPoints); // 将额外点加入当前区域列表

            // 批量获取所有点的类型（包括额外点）
            Map<String, String> typeMap = getLocationTypes(allPoints);

            // 分批处理坐标点（逻辑不变，但处理的是allPoints）
            int totalPoints = allPoints.size();
            int batches = (int) Math.ceil((double) totalPoints / MAX_COORDINATES_PER_BATCH);

            for (int batch = 0; batch < batches; batch++) {
                int startIdx = batch * MAX_COORDINATES_PER_BATCH;
                int endIdx = Math.min(startIdx + MAX_COORDINATES_PER_BATCH, totalPoints);
                List<Accumulation> batchAccumulations = allPoints.subList(startIdx, endIdx);

                // 使用Table服务批量计算距离（逻辑不变）
                List<org.json.JSONObject> batchResults = calculateBatchDistances(batchAccumulations);

                // 准备批量插入数据
                List<Object[]> batchArgs = new ArrayList<>();

                for (int i = 0; i < batchResults.size(); i++) {
                    JSONObject result = batchResults.get(i);
                    int sourceIdx = result.getInt("sourceIdx");
                    int destIdx = result.getInt("destIdx");
                    Accumulation start = batchAccumulations.get(sourceIdx);
                    Accumulation end = batchAccumulations.get(destIdx);

                    // 跳过额外点之间的组合（可选）
                    if (extraPoints.contains(start) && extraPoints.contains(end)) {
                        continue;
                    }

                    double dist = result.getDouble("distance");
                    boolean hasHighway = result.getBoolean("hasHighway");

                    String keyStart = start.getLongitude() + "_" + start.getLatitude();
                    String keyEnd = end.getLongitude() + "_" + end.getLatitude();
                    String typeStart = typeMap.getOrDefault(keyStart, "1");
                    String typeEnd = typeMap.getOrDefault(keyEnd, "1");

                    double time = calculateTime(
                            dist, hasHighway, typeStart, typeEnd,
                            TownshipDrivingSpeed, TownDrivingSpeed, highwaySpeed
                    );

                    batchArgs.add(new Object[]{
                            start.getLongitude(), start.getLatitude(),
                            end.getLongitude(), end.getLatitude(),
                            time
                    });
                }

                // 批量插入本批结果
                if (!batchArgs.isEmpty()) {
                    String insertSql = "INSERT INTO travel_time " +
                            "(longitude_start, latitude_start, longitude_end, latitude_end, travel_time) " +
                            "VALUES (?, ?, ?, ?, ?)";
                    jdbcTemplate.batchUpdate(insertSql, batchArgs);
                }
            }
        }
        test07();
    }

    /*
    * 1.由于经过test06方法后，存在一些坐标是计算不了距离的，所以在数据库中，查询出那些距离为0的坐标，重新调用高德地图api进行计算
    * 2.由于经过test006会出现距离为负数的，现在调用高德填充那些距离为负数
    * */
    public void test07() throws ApiKeyException {
        String sql="select * from travel_time where travel_time <= 0 ";
        List<TravelTime> travelTimes = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(TravelTime.class));
        System.out.println(travelTimes);
        //调用高德地图计算
        for (TravelTime travelTime : travelTimes) {
            double travelTimeBylonlat = getTravelTimeBylonlat(Double.parseDouble(travelTime.getLongitudeStart()), Double.parseDouble(travelTime.getLatitudeStart()), Double.parseDouble(travelTime.getLongitudeEnd()), Double.parseDouble(travelTime.getLatitudeEnd()));
            String updateSql="update travel_time set travel_time=? where longitude_start=? and latitude_start=? and longitude_end=? and latitude_end=?";
            jdbcTemplate.update(updateSql,travelTimeBylonlat,travelTime.getLongitudeStart(),travelTime.getLatitudeStart(),travelTime.getLongitudeEnd(),travelTime.getLatitudeEnd());
        }
    }




    // 批量获取位置类型
    private Map<String, String> getLocationTypes(List<Accumulation> accumulations) {
        List<Double> lngList = new ArrayList<>();
        List<Double> latList = new ArrayList<>();
        for (Accumulation acc : accumulations) {
            lngList.add(acc.getLongitude());
            latList.add(acc.getLatitude());
        }

        String sql = "SELECT longitude, latitude, location_type FROM store " +
                "WHERE (longitude, latitude) IN (";
        StringBuilder placeholders = new StringBuilder();
        for (int i = 0; i < lngList.size(); i++) {
            if (i > 0) placeholders.append(",");
            placeholders.append("(?, ?)");
        }
        sql += placeholders + ") AND is_delete=0";

        List<Object> params = new ArrayList<>();
        for (int i = 0; i < lngList.size(); i++) {
            params.add(lngList.get(i));
            params.add(latList.get(i));
        }

        Map<String, String> typeMap = new HashMap<>();
        jdbcTemplate.query(sql, params.toArray(), (rs) -> {
            String key = rs.getDouble("longitude") + "_" + rs.getDouble("latitude");
            typeMap.put(key, rs.getString("location_type"));
        });

        return typeMap;
    }

    // 使用Table服务批量计算距离和高速状态
    private List<JSONObject> calculateBatchDistances(List<Accumulation> accumulations) throws JSONException {
        // 准备坐标字符串
        StringBuilder coordBuilder = new StringBuilder();
        for (Accumulation acc : accumulations) {
            if (coordBuilder.length() > 0) coordBuilder.append(";");
            coordBuilder.append(acc.getLongitude())
                    .append(",")
                    .append(acc.getLatitude());
        }

        List<JSONObject> results = new ArrayList<>();

        // 首先尝试OSRM批量计算
        try {
            // 调用OSRM Table服务
            String url = "http://192.168.79.130:5000/table/v1/driving/" + coordBuilder + "?annotations=distance";
            JSONObject response = (JSONObject) OSRMTool.getDistancesAndHighwayStatus(url);

            // 解析距离矩阵
            JSONArray distances = response.getJSONArray("distances");

            // 遍历矩阵，跳过对角线（自身距离）
            for (int i = 0; i < distances.length(); i++) {
                JSONArray row = distances.getJSONArray(i);
                for (int j = 0; j < row.length(); j++) {
                    if (i != j) {
                        double distance = row.getDouble(j);

                        // 这里简化处理高速状态，实际应根据业务逻辑实现
                        boolean hasHighway = distance > 10000; // 假设超过10km可能走高速

                        JSONObject result = new JSONObject();
                        result.put("sourceIdx", i);
                        result.put("destIdx", j);
                        result.put("distance", distance);
                        result.put("hasHighway", hasHighway);

                        results.add(result);
                    }
                }
            }

            System.out.println("✅ OSRM批量计算成功，处理了 " + results.size() + " 个点对");

        } catch (Exception e) {
            System.out.println("❌ OSRM不可用，降级到高德API逐个计算: " + e.getMessage());

            // 降级到高德API逐个计算
            for (int i = 0; i < accumulations.size(); i++) {
                for (int j = 0; j < accumulations.size(); j++) {
                    if (i != j) {
                        Accumulation start = accumulations.get(i);
                        Accumulation end = accumulations.get(j);

                        try {
                            // 使用高德API计算
                            String origin = start.getLongitude() + "," + start.getLatitude();
                            String destination = end.getLongitude() + "," + end.getLatitude();

                            // 调用高德API（这里简化，实际应该调用AmapApiService）
                            double distance = calculateHaversineDistance(start, end) * 1000; // 转为米
                            boolean hasHighway = distance > 10000;

                            JSONObject result = new JSONObject();
                            result.put("sourceIdx", i);
                            result.put("destIdx", j);
                            result.put("distance", distance);
                            result.put("hasHighway", hasHighway);

                            results.add(result);

                            // API调用间隔
                            Thread.sleep(50);

                        } catch (Exception apiException) {
                            System.out.println("⚠️  高德API调用失败，使用距离估算: " + apiException.getMessage());

                            // 使用距离估算作为最后备用
                            double distance = calculateHaversineDistance(start, end) * 1000;
                            boolean hasHighway = distance > 10000;

                            JSONObject result = new JSONObject();
                            result.put("sourceIdx", i);
                            result.put("destIdx", j);
                            result.put("distance", distance);
                            result.put("hasHighway", hasHighway);

                            results.add(result);
                        }
                    }
                }
            }

            System.out.println("✅ 高德API/距离估算完成，处理了 " + results.size() + " 个点对");
        }

        return results;
    }
    // 计算时间的方法
    private double calculateTime(double dist, boolean hasHighway,
                                 String typeStart, String typeEnd,
                                 double townshipSpeed, double townSpeed, double highwaySpeed) {
        if (hasHighway) {
            return dist / 1000 / highwaySpeed * 60;
        }

        if ("1".equals(typeStart)) {
            if (typeStart.equals(typeEnd)) {
                return dist / 1000 / townshipSpeed * 60;
            } else {
                double avgSpeed = (townSpeed + townshipSpeed) / 2;
                return dist / 1000 / avgSpeed * 60;
            }
        } else {
            if (typeStart.equals(typeEnd)) {
                return dist / 1000 / townSpeed * 60;
            } else {
                double avgSpeed = (townSpeed + townshipSpeed) / 2;
                return dist / 1000 / avgSpeed * 60;
            }
        }
    }


    //查数据库-根据打卡点的经度纬度获取卸货时长
    double getUnloadingTime(double longitude,double latitude){
        String sql="select unloading_time from unloading_time where acclongitude=? and acclatitude=? limit 1";
        Double time = jdbcTemplate.queryForObject(sql, new Object[]{longitude, latitude}, Double.class);
        return time;
    }

    //查数据库-根据起始打卡点和终点打卡点查询行驶时长
    double getTravelTime(double longitudeStart,double latitudeStart,double longitudeEnd,double latitudeEnd){
        String sql="select travel_time from travel_time where longitude_start=? and latitude_start=? and longitude_end=? and latitude_end=?";
        Double time = jdbcTemplate.queryForObject(sql, new Object[]{longitudeStart, latitudeStart, longitudeEnd, latitudeEnd}, Double.class);
        return time;
    }



    /*
    * 输入打卡点的经纬度，获取卸货时长
    * */
    double getUnloadingTimeBylonlat(double longitude,double latitude){
        Accumulation accumulation = accumulationMapper.selectOne(new LambdaQueryWrapper<Accumulation>().eq(Accumulation::getLongitude, longitude).eq(Accumulation::getLatitude, latitude).eq(Accumulation::getIsDelete, 0).last("limit 1"));

        //获取时长相关的参数
        SystemParameter sp = systemParameterMapper.selectById(1);
        //乡镇平均卸货时长(分钟)
        double shoreUnloadTownshipTime = sp.getShoreUnloadTownshipTime();
        //城镇平均卸货时长（分钟）
        double shoreUnloadCityTime = sp.getShoreUnloadCityTime();

        //乡镇类型的商铺数量
        Long townshipCount = storeMapper.selectCount(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulation.getAccumulationId()).eq(Store::getIsDelete, 0).eq(Store::getLocationType, 1));

        //城镇类型的商铺数量
        Long townCount = storeMapper.selectCount(new LambdaQueryWrapper<Store>().eq(Store::getAccumulationId, accumulation.getAccumulationId()).eq(Store::getIsDelete, 0).eq(Store::getLocationType, 0));

        //统计总时长(分钟)
        double timeSum = 0;
        timeSum = townshipCount * shoreUnloadTownshipTime + townCount * shoreUnloadCityTime;
        return timeSum;
    }


    /*
    * 输入前后两个打卡点的经纬度，获取行驶时长
    * */
    double getTravelTimeBylonlat(double longitudeStart,double latitudeStart,double longitudeEnd,double latitudeEnd) throws ApiKeyException {
        //获取时长相关的参数
        SystemParameter sp = systemParameterMapper.selectById(1);
        //乡镇行驶速度（km/h）
        double TownshipDrivingSpeed = sp.getTownshipRoads();
        //城镇行驶速度（km/h）
        double TownDrivingSpeed = sp.getUrbanRoads();
        //高速公路行驶速度
        double highwaySpeed = sp.getFreeway();


        //获取这个打卡点的类型
        String typeStart;
        try {
            typeStart = jdbcTemplate.queryForObject("select location_type from store where longitude=? and latitude=? and is_delete=0 limit 1", new Object[]{longitudeStart,latitudeStart}, String.class);
        } catch (DataAccessException e) {
            typeStart="1";
        }

        //获取这个打卡点的类型
        String typeEnd;
        try {
            typeEnd = jdbcTemplate.queryForObject("select location_type from store where longitude=? and latitude=? and is_delete=0 limit 1 ", new Object[]{longitudeEnd,latitudeEnd}, String.class);
        } catch (DataAccessException e) {
            typeEnd= "1";
        }
        double[] doubles = saveDistanceInformation(new DoublePoint(new double[]{longitudeStart, latitudeStart}), new DoublePoint(new double[]{longitudeEnd, latitudeEnd}), "");

        //行驶时长(分钟)
        double time = 0;
        double dist = doubles[0];
        if (doubles[1] > 0) {
            //这个高速公路
            time = dist / 1000 / highwaySpeed * 60;
        } else {
            if ("1".equals(typeStart)) {
                if (typeStart.equals(typeEnd)) {
                    time = dist / 1000 / TownshipDrivingSpeed * 60;
                } else {
                    //折中
                    time = dist / 1000 / ((TownDrivingSpeed + TownshipDrivingSpeed) / 2) * 60;
                }
            } else {
                if (typeStart.equals(typeEnd)) {
                    time = dist / 1000 / TownDrivingSpeed * 60;
                } else {
                    //折中
                    time = dist / 1000 / ((TownDrivingSpeed + TownshipDrivingSpeed) / 2) * 60;
                }
            }
        }
        return time;
    }

    public void test001() throws ApiKeyException {
        double unloadingTime = getUnloadingTimeBylonlat(114.079267, 24.676201);
        System.out.println(unloadingTime);

        double travelTime = getTravelTimeBylonlat(114.209602, 25.224353, 114.014315, 25.182799);
        System.out.println(travelTime);
    }

    /**
     * 使用Haversine公式计算两点间距离（公里）
     */
    private double calculateHaversineDistance(Accumulation start, Accumulation end) {
        double lat1 = Math.toRadians(start.getLatitude());
        double lon1 = Math.toRadians(start.getLongitude());
        double lat2 = Math.toRadians(end.getLatitude());
        double lon2 = Math.toRadians(end.getLongitude());

        double dlat = lat2 - lat1;
        double dlon = lon2 - lon1;

        double a = Math.sin(dlat / 2) * Math.sin(dlat / 2) +
                   Math.cos(lat1) * Math.cos(lat2) *
                   Math.sin(dlon / 2) * Math.sin(dlon / 2);

        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

        // 地球半径（公里）
        double earthRadius = 6371.0;

        return earthRadius * c;
    }

}
