#spring:
#  application:
#    # 应用名称
#    name: pathcalculate
#  datasource:
#    url: *******************************************************************************************************&
#    username: root
#    password: 123
#    driver-class-name: com.mysql.jdbc.Driver

# 算法配置
algorithm:
  clustering:
    post-optimization:
      enabled: true

# 临时配置 - 修复启动问题
jjking:
  dbPath: ./temp/masterDatasource.txt

# 启用前辈的时间矩阵生成方法
legacy:
  travel-time:
    enabled: true

# 日志配置 - 屏蔽第三方库的DEBUG日志
logging:
  level:
    # 第三方库日志控制
    com.graphhopper.jsprit: WARN
    org.optaplanner: WARN
    org.drools: WARN
    # 算法详细调试日志
    com.ict.ycwl.pathcalculate.algorithm: INFO
