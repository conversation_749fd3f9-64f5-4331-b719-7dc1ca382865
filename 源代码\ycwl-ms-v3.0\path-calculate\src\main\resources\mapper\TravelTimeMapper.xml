<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper">

    <!-- 批量插入行驶时间数据 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.longitudeStart}, #{item.latitudeStart}, #{item.longitudeEnd}, #{item.latitudeEnd}, #{item.travelTime})
        </foreach>
        ON DUPLICATE KEY UPDATE
        travel_time = VALUES(travel_time)
    </insert>

    <!-- 根据起点和终点坐标查询行驶时间 -->
    <select id="selectTravelTime" resultType="java.lang.Double">
        SELECT travel_time 
        FROM travel_time 
        WHERE longitude_start = #{longitudeStart} 
          AND latitude_start = #{latitudeStart}
          AND longitude_end = #{longitudeEnd}
          AND latitude_end = #{latitudeEnd}
        LIMIT 1
    </select>

    <!-- 检查指定坐标对是否存在行驶时间数据 -->
    <select id="existsTravelTime" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM travel_time 
        WHERE longitude_start = #{longitudeStart} 
          AND latitude_start = #{latitudeStart}
          AND longitude_end = #{longitudeEnd}
          AND latitude_end = #{latitudeEnd}
        LIMIT 1
    </select>

    <!-- 获取指定坐标列表的所有行驶时间数据 -->
    <select id="selectByCoordinates" resultType="com.ict.ycwl.pathcalculate.pojo.TravelTime">
        SELECT longitude_start, latitude_start, longitude_end, latitude_end, travel_time
        FROM travel_time
        WHERE CONCAT(longitude_start, ',', latitude_start) IN
        <foreach collection="coordinates" item="coord" open="(" separator="," close=")">
            #{coord}
        </foreach>
        AND CONCAT(longitude_end, ',', latitude_end) IN
        <foreach collection="coordinates" item="coord" open="(" separator="," close=")">
            #{coord}
        </foreach>
    </select>

    <!-- 删除指定中转站相关的所有行驶时间数据 -->
    <delete id="deleteByCoordinates">
        DELETE FROM travel_time
        WHERE CONCAT(longitude_start, ',', latitude_start) IN
        <foreach collection="coordinates" item="coord" open="(" separator="," close=")">
            #{coord}
        </foreach>
        AND CONCAT(longitude_end, ',', latitude_end) IN
        <foreach collection="coordinates" item="coord" open="(" separator="," close=")">
            #{coord}
        </foreach>
    </delete>

    <!-- 统计指定坐标列表的时间矩阵覆盖度 -->
    <select id="getCoverageStats" resultType="com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper$CoverageStats">
        SELECT 
            (SELECT COUNT(*) * (COUNT(*) - 1) FROM (
                <foreach collection="coordinates" item="coord" separator=" UNION ALL ">
                    SELECT #{coord} as coord
                </foreach>
            ) coords) as totalExpectedPairs,
            COUNT(*) as actualPairs,
            CASE 
                WHEN (SELECT COUNT(*) * (COUNT(*) - 1) FROM (
                    <foreach collection="coordinates" item="coord" separator=" UNION ALL ">
                        SELECT #{coord} as coord
                    </foreach>
                ) coords) > 0 
                THEN COUNT(*) * 1.0 / (SELECT COUNT(*) * (COUNT(*) - 1) FROM (
                    <foreach collection="coordinates" item="coord" separator=" UNION ALL ">
                        SELECT #{coord} as coord
                    </foreach>
                ) coords)
                ELSE 0.0 
            END as coverageRate
        FROM travel_time
        WHERE CONCAT(longitude_start, ',', latitude_start) IN
        <foreach collection="coordinates" item="coord" open="(" separator="," close=")">
            #{coord}
        </foreach>
        AND CONCAT(longitude_end, ',', latitude_end) IN
        <foreach collection="coordinates" item="coord" open="(" separator="," close=")">
            #{coord}
        </foreach>
        AND CONCAT(longitude_start, ',', latitude_start) != CONCAT(longitude_end, ',', latitude_end)
    </select>

</mapper>
