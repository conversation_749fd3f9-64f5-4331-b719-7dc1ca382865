-- =====================================================
-- 粤北卷烟物流管理平台 - travel_time表数据修复脚本（第二部分）
-- 处理其他中转站的数据修复
-- =====================================================

-- 8. 修复翁源县中转站 (ID=3) - 中转站到聚集区
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    td.longitude,
    td.latitude,
    CAST(a.longitude AS CHAR),
    CAST(a.latitude AS CHAR),
    GREATEST(2.0, LEAST(120.0, 
        3.0 + (ABS(CAST(td.longitude AS DECIMAL(10,6)) - a.longitude) + 
               ABS(CAST(td.latitude AS DECIMAL(10,6)) - a.latitude)) * 800
    ))
FROM transit_depot td
CROSS JOIN accumulation a
WHERE td.transit_depot_id = 3 
  AND a.transit_depot_id = 3
  AND td.is_delete = 0 
  AND a.is_delete = 0 
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = td.longitude 
        AND tt.latitude_start = td.latitude
        AND tt.longitude_end = CAST(a.longitude AS CHAR)
        AND tt.latitude_end = CAST(a.latitude AS CHAR)
  );

-- 9. 修复翁源县中转站 (ID=3) - 聚集区到中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a.longitude AS CHAR),
    CAST(a.latitude AS CHAR),
    td.longitude,
    td.latitude,
    GREATEST(2.0, LEAST(120.0, 
        3.0 + (ABS(a.longitude - CAST(td.longitude AS DECIMAL(10,6))) + 
               ABS(a.latitude - CAST(td.latitude AS DECIMAL(10,6)))) * 800
    ))
FROM accumulation a
CROSS JOIN transit_depot td
WHERE a.transit_depot_id = 3 
  AND td.transit_depot_id = 3
  AND a.is_delete = 0 
  AND td.is_delete = 0
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = CAST(a.longitude AS CHAR)
        AND tt.latitude_start = CAST(a.latitude AS CHAR)
        AND tt.longitude_end = td.longitude
        AND tt.latitude_end = td.latitude
  );

-- 10. 修复翁源县中转站 (ID=3) - 聚集区之间（分批处理）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR),
    CAST(a1.latitude AS CHAR),
    CAST(a2.longitude AS CHAR),
    CAST(a2.latitude AS CHAR),
    GREATEST(1.0, LEAST(180.0, 
        2.0 + (ABS(a1.longitude - a2.longitude) + 
               ABS(a1.latitude - a2.latitude)) * 1000
    ))
FROM accumulation a1
CROSS JOIN accumulation a2
WHERE a1.transit_depot_id = 3 
  AND a2.transit_depot_id = 3
  AND a1.accumulation_id != a2.accumulation_id
  AND a1.is_delete = 0 
  AND a2.is_delete = 0
  AND a1.longitude != 0.0 AND a1.latitude != 0.0
  AND a2.longitude != 0.0 AND a2.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = CAST(a1.longitude AS CHAR)
        AND tt.latitude_start = CAST(a1.latitude AS CHAR)
        AND tt.longitude_end = CAST(a2.longitude AS CHAR)
        AND tt.latitude_end = CAST(a2.latitude AS CHAR)
  )
LIMIT 20000;

-- 11. 批量处理所有中转站的基础连接（中转站<->聚集区）
-- 使用简化算法快速生成基础数据
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    td.longitude,
    td.latitude,
    CAST(a.longitude AS CHAR),
    CAST(a.latitude AS CHAR),
    GREATEST(3.0, LEAST(90.0, 
        5.0 + SQRT(
            POW(CAST(td.longitude AS DECIMAL(10,6)) - a.longitude, 2) + 
            POW(CAST(td.latitude AS DECIMAL(10,6)) - a.latitude, 2)
        ) * 5000
    ))
FROM transit_depot td
CROSS JOIN accumulation a
WHERE td.transit_depot_id = a.transit_depot_id
  AND td.transit_depot_id IN (2, 4, 5, 6) -- 剩余的中转站
  AND td.is_delete = 0 
  AND a.is_delete = 0 
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = td.longitude 
        AND tt.latitude_start = td.latitude
        AND tt.longitude_end = CAST(a.longitude AS CHAR)
        AND tt.latitude_end = CAST(a.latitude AS CHAR)
  );

-- 12. 反向：聚集区到中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a.longitude AS CHAR),
    CAST(a.latitude AS CHAR),
    td.longitude,
    td.latitude,
    GREATEST(3.0, LEAST(90.0, 
        5.0 + SQRT(
            POW(a.longitude - CAST(td.longitude AS DECIMAL(10,6)), 2) + 
            POW(a.latitude - CAST(td.latitude AS DECIMAL(10,6)), 2)
        ) * 5000
    ))
FROM accumulation a
CROSS JOIN transit_depot td
WHERE a.transit_depot_id = td.transit_depot_id
  AND td.transit_depot_id IN (2, 4, 5, 6)
  AND a.is_delete = 0 
  AND td.is_delete = 0
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = CAST(a.longitude AS CHAR)
        AND tt.latitude_start = CAST(a.latitude AS CHAR)
        AND tt.longitude_end = td.longitude
        AND tt.latitude_end = td.latitude
  );

-- 13. 快速生成聚集区间数据（较小中转站优先）
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR),
    CAST(a1.latitude AS CHAR),
    CAST(a2.longitude AS CHAR),
    CAST(a2.latitude AS CHAR),
    GREATEST(1.5, LEAST(150.0, 
        3.0 + SQRT(
            POW(a1.longitude - a2.longitude, 2) + 
            POW(a1.latitude - a2.latitude, 2)
        ) * 6000
    ))
FROM accumulation a1
CROSS JOIN accumulation a2
WHERE a1.transit_depot_id = a2.transit_depot_id
  AND a1.accumulation_id != a2.accumulation_id
  AND a1.transit_depot_id IN (2, 3, 4) -- 优先处理中等规模的中转站
  AND a1.is_delete = 0 
  AND a2.is_delete = 0
  AND a1.longitude != 0.0 AND a1.latitude != 0.0
  AND a2.longitude != 0.0 AND a2.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = CAST(a1.longitude AS CHAR)
        AND tt.latitude_start = CAST(a1.latitude AS CHAR)
        AND tt.longitude_end = CAST(a2.longitude AS CHAR)
        AND tt.latitude_end = CAST(a2.latitude AS CHAR)
  )
LIMIT 50000; -- 限制数量避免超时

-- 14. 检查修复进度
SELECT 
    '=== 修复进度检查 ===' as progress_check,
    COUNT(*) as total_records,
    677804 as expected_total,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as current_coverage_percent,
    CASE 
        WHEN COUNT(*) * 100.0 / 677804 >= 80.0 THEN '✅ 已达到算法要求，可以测试/calculateAll接口'
        WHEN COUNT(*) * 100.0 / 677804 >= 60.0 THEN '⚠️ 接近目标，建议继续执行大中转站的聚集区间数据生成'
        ELSE '❌ 需要继续修复'
    END as status,
    CONCAT('距离80%目标还需要约 ', GREATEST(0, CEIL(677804 * 0.8) - COUNT(*)), ' 条记录') as remaining_info
FROM travel_time;

-- 15. 按中转站显示覆盖情况
SELECT 
    '=== 各中转站覆盖情况 ===' as coverage_by_depot,
    td.transit_depot_id,
    td.transit_depot_name,
    COUNT(a.accumulation_id) + 1 as total_points,
    (COUNT(a.accumulation_id) + 1) * COUNT(a.accumulation_id) as expected_pairs,
    COALESCE(coverage.actual_pairs, 0) as actual_pairs,
    ROUND(COALESCE(coverage.actual_pairs, 0) * 100.0 / ((COUNT(a.accumulation_id) + 1) * COUNT(a.accumulation_id)), 2) as depot_coverage_percent
FROM transit_depot td 
LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
    AND a.is_delete = 0 
    AND a.longitude != 0.0 
    AND a.latitude != 0.0
LEFT JOIN (
    -- 简化的覆盖度统计（近似值）
    SELECT 
        td2.transit_depot_id,
        COUNT(*) as actual_pairs
    FROM transit_depot td2
    JOIN travel_time tt ON (
        tt.longitude_start = td2.longitude OR tt.longitude_end = td2.longitude
    )
    WHERE td2.is_delete = 0
    GROUP BY td2.transit_depot_id
) coverage ON td.transit_depot_id = coverage.transit_depot_id
WHERE td.is_delete = 0
GROUP BY td.transit_depot_id, td.transit_depot_name, coverage.actual_pairs
ORDER BY depot_coverage_percent DESC;
