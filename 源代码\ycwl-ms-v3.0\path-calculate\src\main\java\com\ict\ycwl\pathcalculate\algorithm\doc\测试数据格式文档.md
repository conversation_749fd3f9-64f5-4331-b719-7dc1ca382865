# 路径规划算法测试数据格式文档

## 📋 概述

为了便于算法开发和测试，将路径规划算法的输入数据分为4个独立的JSON文件：

1. `accumulations.json` - 聚集区数据
2. `transit_depots.json` - 中转站数据  
3. `teams.json` - 班组数据
4. `time_matrix.json` - 时间矩阵数据

## 📁 文件结构

```
test_data/
├── accumulations.json        # 聚集区数据
├── transit_depots.json      # 中转站数据
├── teams.json               # 班组数据
└── time_matrix.json         # 时间矩阵数据
```

## 📄 各文件格式详解

### 1. accumulations.json - 聚集区数据

```json
{
  "description": "聚集区数据，包含坐标、配送时间等信息",
  "version": "1.0.0",
  "data": [
    {
      "accumulationId": 1001,
      "accumulationName": "韶关市聚集区1",
      "longitude": 113.596766,
      "latitude": 24.810403,
      "transitDepotId": 2001,
      "deliveryTime": 15.5
    },
    {
      "accumulationId": 1002,
      "accumulationName": "韶关市聚集区2", 
      "longitude": 113.598123,
      "latitude": 24.812456,
      "transitDepotId": 2001,
      "deliveryTime": 12.8
    },
    {
      "accumulationId": 1003,
      "accumulationName": "乐昌市聚集区1",
      "longitude": 113.352817,
      "latitude": 25.128375,
      "transitDepotId": 2002,
      "deliveryTime": 18.2
    }
  ]
}
```

**字段说明**：
- `accumulationId`: 聚集区唯一ID
- `accumulationName`: 聚集区名称
- `longitude`: 经度（保留6位小数）
- `latitude`: 纬度（保留6位小数）
- `transitDepotId`: 所属中转站ID
- `deliveryTime`: 配送时间（分钟）- 点权

### 2. transit_depots.json - 中转站数据

```json
{
  "description": "中转站数据，包含坐标、所属班组、路线数量等信息",
  "version": "1.0.0",
  "data": [
    {
      "transitDepotId": 2001,
      "transitDepotName": "韶关市中转站",
      "longitude": 113.596766,
      "latitude": 24.810403,
      "groupId": 3001,
      "routeCount": 5
    },
    {
      "transitDepotId": 2002,
      "transitDepotName": "乐昌市中转站",
      "longitude": 113.352817,
      "latitude": 25.128375,
      "groupId": 3002,
      "routeCount": 3
    },
    {
      "transitDepotId": 2003,
      "transitDepotName": "南雄市中转站",
      "longitude": 114.308259,
      "latitude": 25.123456,
      "groupId": 3003,
      "routeCount": 4
    }
  ]
}
```

**字段说明**：
- `transitDepotId`: 中转站唯一ID
- `transitDepotName`: 中转站名称
- `longitude`: 经度（保留6位小数）
- `latitude`: 纬度（保留6位小数）
- `groupId`: 所属班组ID
- `routeCount`: 规划路线数量

### 3. teams.json - 班组数据

```json
{
  "description": "班组数据，包含班组层级关系",
  "version": "1.0.0",
  "data": [
    {
      "teamId": 3001,
      "teamName": "班组一",
      "transitDepotIds": [2001]
    },
    {
      "teamId": 3002,
      "teamName": "班组二",
      "transitDepotIds": [2002]
    },
    {
      "teamId": 3003,
      "teamName": "班组三",
      "transitDepotIds": [2003]
    }
  ]
}
```

**字段说明**：
- `teamId`: 班组唯一ID
- `teamName`: 班组名称
- `transitDepotIds`: 班组下所有中转站ID列表

### 4. time_matrix.json - 时间矩阵数据

```json
{
  "description": "时间矩阵数据，包含所有点对点的行驶时间",
  "version": "1.0.0",
  "data": {
    "113.596766,24.810403->113.598123,24.812456": {
      "fromLongitude": 113.596766,
      "fromLatitude": 24.810403,
      "toLongitude": 113.598123,
      "toLatitude": 24.812456,
      "travelTime": 3.5
    },
    "113.598123,24.812456->113.352817,25.128375": {
      "fromLongitude": 113.598123,
      "fromLatitude": 24.812456,
      "toLongitude": 113.352817,
      "toLatitude": 25.128375,
      "travelTime": 45.2
    },
    "113.352817,25.128375->113.596766,24.810403": {
      "fromLongitude": 113.352817,
      "fromLatitude": 25.128375,
      "toLongitude": 113.596766,
      "toLatitude": 24.810403,
      "travelTime": 42.8
    }
  }
}
```

**字段说明**：
- Key格式：`"起点经度,起点纬度->终点经度,终点纬度"`
- `fromLongitude`: 起点经度
- `fromLatitude`: 起点纬度  
- `toLongitude`: 终点经度
- `toLatitude`: 终点纬度
- `travelTime`: 行驶时间（分钟）- 边权

## 📊 数据规模建议

### 小规模测试数据
- 聚集区：20-50个
- 中转站：3-5个
- 班组：2-3个

### 中等规模测试数据
- 聚集区：100-200个
- 中转站：8-10个
- 班组：5-6个

### 大规模测试数据
- 聚集区：300-500个
- 中转站：15-20个
- 班组：8-10个

## 📤 算法输出数据格式示例

### 路线结果包含凸包信息
```json
{
  "success": true,
  "errorMessage": null,
  "executionTime": 1250,
  "routes": [
    {
      "routeId": 5001,
      "routeName": "韶关市中转站-路线1",
      "transitDepotId": 2001,
      "accumulationSequence": [1001, 1002, 1003],
      "polyline": [
        {"longitude": 113.596766, "latitude": 24.810403},
        {"longitude": 113.598123, "latitude": 24.812456},
        {"longitude": 113.600234, "latitude": 24.815678},
        {"longitude": 113.596766, "latitude": 24.810403}
      ],
      "totalWorkTime": 45.5,
      "convexHull": [
        {"longitude": 113.596766, "latitude": 24.810403},
        {"longitude": 113.598123, "latitude": 24.812456},
        {"longitude": 113.600234, "latitude": 24.815678},
        {"longitude": 113.596766, "latitude": 24.810403}
      ]
    }
  ],
  "timeBalanceStats": {
    "routeTimeGapByDepot": {
      "2001": 5.2,
      "2002": 3.8
    },
    "depotTimeGapByTeam": {
      "3001": 8.5,
      "3002": 6.3
    },
    "teamTimeGap": 12.7
  }
}
```

**凸包字段说明**：
- `convexHull`: 路线凸包坐标点串，包含该路线所有聚集区的最小凸多边形
- 凸包坐标点按逆时针顺序排列，首尾相接形成闭合多边形
- 保证同一中转站下的路线凸包不重叠
- 用于路线区域可视化和配送区域划分 