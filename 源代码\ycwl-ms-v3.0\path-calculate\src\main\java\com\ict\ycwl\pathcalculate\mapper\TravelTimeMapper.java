package com.ict.ycwl.pathcalculate.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ict.ycwl.pathcalculate.pojo.TravelTime;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 行驶时间数据访问层
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Mapper
public interface TravelTimeMapper extends BaseMapper<TravelTime> {
    
    /**
     * 批量插入行驶时间数据
     * 
     * @param travelTimes 行驶时间数据列表
     * @return 插入的记录数
     */
    int batchInsert(@Param("list") List<TravelTime> travelTimes);
    
    /**
     * 根据起点和终点坐标查询行驶时间
     * 
     * @param longitudeStart 起点经度
     * @param latitudeStart 起点纬度
     * @param longitudeEnd 终点经度
     * @param latitudeEnd 终点纬度
     * @return 行驶时间（分钟）
     */
    Double selectTravelTime(@Param("longitudeStart") String longitudeStart,
                           @Param("latitudeStart") String latitudeStart,
                           @Param("longitudeEnd") String longitudeEnd,
                           @Param("latitudeEnd") String latitudeEnd);
    
    /**
     * 检查指定坐标对是否存在行驶时间数据
     * 
     * @param longitudeStart 起点经度
     * @param latitudeStart 起点纬度
     * @param longitudeEnd 终点经度
     * @param latitudeEnd 终点纬度
     * @return 是否存在
     */
    boolean existsTravelTime(@Param("longitudeStart") String longitudeStart,
                            @Param("latitudeStart") String latitudeStart,
                            @Param("longitudeEnd") String longitudeEnd,
                            @Param("latitudeEnd") String latitudeEnd);
    
    /**
     * 获取指定坐标列表的所有行驶时间数据
     * 
     * @param coordinates 坐标列表，格式："经度,纬度"
     * @return 行驶时间数据列表
     */
    List<TravelTime> selectByCoordinates(@Param("coordinates") List<String> coordinates);
    
    /**
     * 删除指定中转站相关的所有行驶时间数据
     * 
     * @param coordinates 该中转站内所有坐标点
     * @return 删除的记录数
     */
    int deleteByCoordinates(@Param("coordinates") List<String> coordinates);
    
    /**
     * 统计指定坐标列表的时间矩阵覆盖度
     * 
     * @param coordinates 坐标列表
     * @return 覆盖度统计信息
     */
    CoverageStats getCoverageStats(@Param("coordinates") List<String> coordinates);
    
    /**
     * 覆盖度统计信息
     */
    class CoverageStats {
        private int totalExpectedPairs;
        private int actualPairs;
        private double coverageRate;
        
        public CoverageStats() {}
        
        public CoverageStats(int totalExpectedPairs, int actualPairs) {
            this.totalExpectedPairs = totalExpectedPairs;
            this.actualPairs = actualPairs;
            this.coverageRate = totalExpectedPairs > 0 ? (double) actualPairs / totalExpectedPairs : 0.0;
        }
        
        // Getters and Setters
        public int getTotalExpectedPairs() { return totalExpectedPairs; }
        public void setTotalExpectedPairs(int totalExpectedPairs) { this.totalExpectedPairs = totalExpectedPairs; }
        
        public int getActualPairs() { return actualPairs; }
        public void setActualPairs(int actualPairs) { this.actualPairs = actualPairs; }
        
        public double getCoverageRate() { return coverageRate; }
        public void setCoverageRate(double coverageRate) { this.coverageRate = coverageRate; }
    }
}
