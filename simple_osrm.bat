@echo off
docker --version
if %errorlevel% neq 0 (
    echo Docker not found. Please install Docker Desktop.
    pause
    exit /b 1
)

echo Starting OSRM...
docker stop osrm-backend 2>nul
docker rm osrm-backend 2>nul

docker run -d --name osrm-backend -p 5000:5000 osrm/osrm-backend

timeout /t 5 /nobreak >nul

curl -s http://localhost:5000/route/v1/driving/13.388860,52.517037;13.397634,52.529407
if %errorlevel% equ 0 (
    echo OSRM is running at http://localhost:5000
) else (
    echo OSRM failed to start
    docker logs osrm-backend
)

pause
