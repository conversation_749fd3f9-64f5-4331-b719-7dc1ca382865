package com.ict.ycwl.pathcalculate;

import com.ict.ycwl.pathcalculate.common.exception.ApiKeyException;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

/**
 * 直接运行前辈方法的工具类
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "legacy.travel-time.enabled", havingValue = "true", matchIfMissing = false)
public class DirectRouteRunner {
    
    @Autowired
    private RouteTest001 routeTest001;
    
    /**
     * 直接运行前辈的test06方法
     */
    public void runTest06() {
        log.info("========================================");
        log.info("开始执行前辈的test06方法");
        log.info("========================================");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 直接调用前辈的test06方法
            routeTest001.test06();
            
            long endTime = System.currentTimeMillis();
            double durationMinutes = (endTime - startTime) / 1000.0 / 60.0;
            
            log.info("========================================");
            log.info("前辈的test06方法执行完成！");
            log.info("耗时: {:.2f} 分钟", durationMinutes);
            log.info("========================================");
            
        } catch (ApiKeyException e) {
            log.error("前辈的test06方法执行失败 - API Key错误: {}", e.getMessage(), e);
        } catch (JSONException e) {
            log.error("前辈的test06方法执行失败 - JSON解析错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("前辈的test06方法执行失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 直接运行前辈的test07方法
     */
    public void runTest07() {
        log.info("========================================");
        log.info("开始执行前辈的test07方法");
        log.info("========================================");
        
        try {
            long startTime = System.currentTimeMillis();
            
            // 直接调用前辈的test07方法
            routeTest001.test07();
            
            long endTime = System.currentTimeMillis();
            double durationMinutes = (endTime - startTime) / 1000.0 / 60.0;
            
            log.info("========================================");
            log.info("前辈的test07方法执行完成！");
            log.info("耗时: {:.2f} 分钟", durationMinutes);
            log.info("========================================");
            
        } catch (ApiKeyException e) {
            log.error("前辈的test07方法执行失败 - API Key错误: {}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("前辈的test07方法执行失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 按前辈的顺序执行完整流程
     */
    public void runAll() {
        log.info("========================================");
        log.info("开始按前辈的顺序执行完整流程");
        log.info("第1步: test06 (主要生成方法)");
        log.info("第2步: test07 (修复无效记录)");
        log.info("========================================");
        
        long totalStartTime = System.currentTimeMillis();
        
        // 第1步：执行test06
        log.info(">>> 第1步：执行前辈的test06方法");
        runTest06();
        
        // 第2步：执行test07
        log.info(">>> 第2步：执行前辈的test07方法");
        runTest07();
        
        long totalEndTime = System.currentTimeMillis();
        double totalDurationMinutes = (totalEndTime - totalStartTime) / 1000.0 / 60.0;
        
        log.info("========================================");
        log.info("前辈的完整流程执行完成！");
        log.info("总耗时: {:.2f} 分钟", totalDurationMinutes);
        log.info("========================================");
    }
}
