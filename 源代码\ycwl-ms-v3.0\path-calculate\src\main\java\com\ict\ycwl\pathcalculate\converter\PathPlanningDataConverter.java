package com.ict.ycwl.pathcalculate.converter;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import com.ict.ycwl.pathcalculate.algorithm.entity.*;
import com.ict.ycwl.pathcalculate.mapper.*;
import com.ict.ycwl.pathcalculate.pojo.dynamiEntity.TeamF;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 路径规划数据转换器
 * 负责将数据库实体转换为算法需要的PathPlanningRequest格式
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@Component
public class PathPlanningDataConverter {

    @Autowired
    private AccumulationMapper accumulationMapper;
    
    @Autowired
    private TransitDepotMapper transitDepotMapper;
    
    @Autowired
    private GroupMapper groupMapper;
    
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 从数据库加载数据并转换为PathPlanningRequest
     * 
     * @return 路径规划请求对象
     */
    public PathPlanningRequest loadPathPlanningRequest() {
        log.info("开始从数据库加载路径规划数据...");
        
        try {
            // 1. 加载聚集区数据
            List<Accumulation> accumulations = loadAccumulations();
            log.info("加载聚集区数据完成，数量: {}", accumulations.size());
            
            // 2. 加载中转站数据
            List<TransitDepot> transitDepots = loadTransitDepots();
            log.info("加载中转站数据完成，数量: {}", transitDepots.size());
            
            // 3. 加载班组数据
            List<Team> teams = loadTeams();
            log.info("加载班组数据完成，数量: {}", teams.size());
            
            // 4. 加载时间矩阵数据
            Map<String, TimeInfo> timeMatrix = loadTimeMatrix();
            log.info("加载时间矩阵数据完成，记录数: {}", timeMatrix.size());
            
            // 5. 构建PathPlanningRequest
            PathPlanningRequest request = PathPlanningRequest.builder()
                    .accumulations(accumulations)
                    .transitDepots(transitDepots)
                    .teams(teams)
                    .timeMatrix(timeMatrix)
                    .build();
            
            // 6. 数据验证
            if (!request.isValid()) {
                log.error("PathPlanningRequest数据验证失败");
                throw new RuntimeException("路径规划请求数据无效");
            }
            
            log.info("路径规划数据加载完成 ✓");
            return request;
            
        } catch (Exception e) {
            log.error("加载路径规划数据失败", e);
            throw new RuntimeException("加载路径规划数据失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载聚集区数据
     */
    private List<Accumulation> loadAccumulations() {
        // 查询有效的聚集区数据
        LambdaQueryWrapper<com.ict.ycwl.pathcalculate.pojo.Accumulation> queryWrapper = 
                new LambdaQueryWrapper<>();
        queryWrapper.eq(com.ict.ycwl.pathcalculate.pojo.Accumulation::getIsDelete, false)
                   .isNotNull(com.ict.ycwl.pathcalculate.pojo.Accumulation::getLongitude)
                   .isNotNull(com.ict.ycwl.pathcalculate.pojo.Accumulation::getLatitude)
                   .ne(com.ict.ycwl.pathcalculate.pojo.Accumulation::getLongitude, 0.0)
                   .ne(com.ict.ycwl.pathcalculate.pojo.Accumulation::getLatitude, 0.0);
        
        List<com.ict.ycwl.pathcalculate.pojo.Accumulation> dbAccumulations = 
                accumulationMapper.selectList(queryWrapper);
        
        // 批量查询配送时间
        Map<Long, Double> deliveryTimeMap = loadDeliveryTimes(dbAccumulations);
        
        // 转换为算法实体
        List<Accumulation> accumulations = new ArrayList<>();
        for (com.ict.ycwl.pathcalculate.pojo.Accumulation dbAcc : dbAccumulations) {
            try {
                Double deliveryTime = deliveryTimeMap.getOrDefault(dbAcc.getAccumulationId(), 15.0);
                
                Accumulation acc = Accumulation.builder()
                        .accumulationId(dbAcc.getAccumulationId())
                        .accumulationName(dbAcc.getAccumulationName())
                        .longitude(dbAcc.getLongitude())
                        .latitude(dbAcc.getLatitude())
                        .transitDepotId(dbAcc.getTransitDepotId())
                        .deliveryTime(deliveryTime)
                        .build();
                
                if (acc.isValid()) {
                    accumulations.add(acc);
                } else {
                    log.warn("聚集区数据无效，跳过: {}", dbAcc.getAccumulationName());
                }
            } catch (Exception e) {
                log.warn("转换聚集区数据失败，跳过: {} - {}", dbAcc.getAccumulationName(), e.getMessage());
            }
        }
        
        return accumulations;
    }

    /**
     * 批量加载配送时间
     */
    private Map<Long, Double> loadDeliveryTimes(List<com.ict.ycwl.pathcalculate.pojo.Accumulation> accumulations) {
        Map<Long, Double> deliveryTimeMap = new HashMap<>();
        
        if (accumulations.isEmpty()) {
            return deliveryTimeMap;
        }
        
        try {
            // 构建批量查询SQL
            List<Long> accIds = accumulations.stream()
                    .map(com.ict.ycwl.pathcalculate.pojo.Accumulation::getAccumulationId)
                    .collect(Collectors.toList());
            
            String sql = "SELECT acc_id, unloading_time FROM unloading_time WHERE acc_id IN (" +
                    accIds.stream().map(id -> "?").collect(Collectors.joining(",")) + ")";
            
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, accIds.toArray());
            
            for (Map<String, Object> row : results) {
                Long accId = ((Number) row.get("acc_id")).longValue();
                Double unloadingTime = ((Number) row.get("unloading_time")).doubleValue();
                deliveryTimeMap.put(accId, unloadingTime);
            }
            
            log.info("成功加载{}个聚集区的配送时间数据", deliveryTimeMap.size());
            
        } catch (Exception e) {
            log.warn("批量加载配送时间失败，将使用默认值: {}", e.getMessage());
        }
        
        return deliveryTimeMap;
    }

    /**
     * 加载中转站数据
     */
    private List<TransitDepot> loadTransitDepots() {
        // 查询有效的中转站数据
        LambdaQueryWrapper<com.ict.ycwl.pathcalculate.pojo.TransitDepot> queryWrapper = 
                new LambdaQueryWrapper<>();
        queryWrapper.eq(com.ict.ycwl.pathcalculate.pojo.TransitDepot::getIsDelete, 0);
        
        List<com.ict.ycwl.pathcalculate.pojo.TransitDepot> dbTransitDepots = 
                transitDepotMapper.selectList(queryWrapper);
        
        // 批量查询路线数量
        Map<Long, Integer> routeCountMap = loadRouteCountsByGroupId();
        
        // 转换为算法实体
        List<TransitDepot> transitDepots = new ArrayList<>();
        for (com.ict.ycwl.pathcalculate.pojo.TransitDepot dbDepot : dbTransitDepots) {
            try {
                // 处理坐标类型转换（数据库中是varchar）
                Double longitude = parseCoordinate(dbDepot.getLongitude());
                Double latitude = parseCoordinate(dbDepot.getLatitude());
                
                if (longitude == null || latitude == null) {
                    log.warn("中转站坐标无效，跳过: {}", dbDepot.getTransitDepotName());
                    continue;
                }
                
                Integer routeCount = routeCountMap.getOrDefault(dbDepot.getGroupId(), 5);
                
                TransitDepot depot = TransitDepot.builder()
                        .transitDepotId(dbDepot.getTransitDepotId())
                        .transitDepotName(dbDepot.getTransitDepotName())
                        .longitude(longitude)
                        .latitude(latitude)
                        .groupId(dbDepot.getGroupId())
                        .routeCount(routeCount)
                        .build();
                
                if (depot.isValid()) {
                    transitDepots.add(depot);
                } else {
                    log.warn("中转站数据无效，跳过: {}", dbDepot.getTransitDepotName());
                }
            } catch (Exception e) {
                log.warn("转换中转站数据失败，跳过: {} - {}", dbDepot.getTransitDepotName(), e.getMessage());
            }
        }
        
        return transitDepots;
    }

    /**
     * 解析坐标字符串为Double
     */
    private Double parseCoordinate(String coordinate) {
        if (coordinate == null || coordinate.trim().isEmpty()) {
            return null;
        }
        
        try {
            return Double.parseDouble(coordinate.trim());
        } catch (NumberFormatException e) {
            log.warn("坐标解析失败: {}", coordinate);
            return null;
        }
    }

    /**
     * 加载各班组的路线数量
     */
    private Map<Long, Integer> loadRouteCountsByGroupId() {
        Map<Long, Integer> routeCountMap = new HashMap<>();
        
        try {
            String sql = "SELECT team_id, route_sum FROM team WHERE is_delete = 0";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
            
            for (Map<String, Object> row : results) {
                Long teamId = ((Number) row.get("team_id")).longValue();
                Integer routeSum = ((Number) row.get("route_sum")).intValue();
                routeCountMap.put(teamId, routeSum);
            }
            
            log.info("成功加载{}个班组的路线数量配置", routeCountMap.size());
            
        } catch (Exception e) {
            log.warn("加载路线数量配置失败，将使用默认值: {}", e.getMessage());
        }
        
        return routeCountMap;
    }

    /**
     * 加载班组数据
     */
    private List<Team> loadTeams() {
        try {
            String sql = "SELECT team_id, team_name FROM team WHERE is_delete = 0";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
            
            List<Team> teams = new ArrayList<>();
            for (Map<String, Object> row : results) {
                Long teamId = ((Number) row.get("team_id")).longValue();
                String teamName = (String) row.get("team_name");
                
                // 查询该班组下的中转站ID列表
                List<Long> transitDepotIds = getTransitDepotIdsByTeamId(teamId);
                
                Team team = Team.builder()
                        .teamId(teamId)
                        .teamName(teamName)
                        .transitDepotIds(transitDepotIds)
                        .build();
                
                if (team.isValid()) {
                    teams.add(team);
                } else {
                    log.warn("班组数据无效，跳过: {}", teamName);
                }
            }
            
            return teams;
            
        } catch (Exception e) {
            log.error("加载班组数据失败", e);
            throw new RuntimeException("加载班组数据失败", e);
        }
    }

    /**
     * 根据班组ID查询中转站ID列表
     */
    private List<Long> getTransitDepotIdsByTeamId(Long teamId) {
        try {
            String sql = "SELECT transit_depot_id FROM transit_depot WHERE group_id = ? AND is_delete = 0";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, teamId);
            
            return results.stream()
                    .map(row -> ((Number) row.get("transit_depot_id")).longValue())
                    .collect(Collectors.toList());
                    
        } catch (Exception e) {
            log.warn("查询班组{}的中转站列表失败: {}", teamId, e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 加载时间矩阵数据
     */
    private Map<String, TimeInfo> loadTimeMatrix() {
        Map<String, TimeInfo> timeMatrix = new HashMap<>();
        
        try {
            String sql = "SELECT longitude_start, latitude_start, longitude_end, latitude_end, travel_time " +
                        "FROM travel_time LIMIT 50000"; // 限制数量避免内存问题
            
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql);
            
            for (Map<String, Object> row : results) {
                try {
                    String lngStart = (String) row.get("longitude_start");
                    String latStart = (String) row.get("latitude_start");
                    String lngEnd = (String) row.get("longitude_end");
                    String latEnd = (String) row.get("latitude_end");
                    Double travelTime = ((Number) row.get("travel_time")).doubleValue();
                    
                    // 构建时间矩阵的键
                    String key = String.format("%s,%s->%s,%s", lngStart, latStart, lngEnd, latEnd);
                    
                    TimeInfo timeInfo = TimeInfo.builder()
                            .fromLongitude(Double.parseDouble(lngStart))
                            .fromLatitude(Double.parseDouble(latStart))
                            .toLongitude(Double.parseDouble(lngEnd))
                            .toLatitude(Double.parseDouble(latEnd))
                            .travelTime(travelTime)
                            .build();
                    
                    timeMatrix.put(key, timeInfo);
                    
                } catch (Exception e) {
                    // 跳过无效的时间矩阵记录
                    continue;
                }
            }
            
            log.info("成功加载{}条时间矩阵记录", timeMatrix.size());
            
        } catch (Exception e) {
            log.error("加载时间矩阵数据失败", e);
            throw new RuntimeException("加载时间矩阵数据失败", e);
        }
        
        return timeMatrix;
    }

    /**
     * 将PathPlanningResult转换为前端需要的ResultRoute格式
     *
     * @param algorithmResult 算法结果
     * @return 前端结果格式
     */
    public List<com.ict.ycwl.pathcalculate.pojo.ResultRoute> convertToResultRoutes(
            com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult algorithmResult) {

        if (algorithmResult == null || !algorithmResult.isSuccess() || algorithmResult.getRoutes() == null) {
            log.warn("算法结果无效，返回空列表");
            return new ArrayList<>();
        }

        List<com.ict.ycwl.pathcalculate.pojo.ResultRoute> resultRoutes = new ArrayList<>();

        for (com.ict.ycwl.pathcalculate.algorithm.entity.RouteResult algorithmRoute : algorithmResult.getRoutes()) {
            try {
                com.ict.ycwl.pathcalculate.pojo.ResultRoute resultRoute = convertSingleRoute(algorithmRoute);
                if (resultRoute != null) {
                    resultRoutes.add(resultRoute);
                }
            } catch (Exception e) {
                log.warn("转换路线结果失败，跳过: {} - {}", algorithmRoute.getRouteName(), e.getMessage());
            }
        }

        log.info("成功转换{}条路线结果", resultRoutes.size());
        return resultRoutes;
    }

    /**
     * 转换单个路线结果
     */
    private com.ict.ycwl.pathcalculate.pojo.ResultRoute convertSingleRoute(
            com.ict.ycwl.pathcalculate.algorithm.entity.RouteResult algorithmRoute) {

        com.ict.ycwl.pathcalculate.pojo.ResultRoute resultRoute =
                new com.ict.ycwl.pathcalculate.pojo.ResultRoute();

        // 基本信息
        resultRoute.setRouteId(algorithmRoute.getRouteId());
        resultRoute.setRouteName(algorithmRoute.getRouteName());
        resultRoute.setTransitDepotId(algorithmRoute.getTransitDepotId());

        // 工作时间
        if (algorithmRoute.getTotalWorkTime() != null) {
            resultRoute.setWorkTime(java.math.BigDecimal.valueOf(algorithmRoute.getTotalWorkTime()));
        }

        // 转换坐标串
        if (algorithmRoute.getPolyline() != null) {
            List<com.ict.ycwl.pathcalculate.pojo.LngAndLat> polyline = new ArrayList<>();
            for (com.ict.ycwl.pathcalculate.algorithm.entity.CoordinatePoint point : algorithmRoute.getPolyline()) {
                com.ict.ycwl.pathcalculate.pojo.LngAndLat lngLat =
                        new com.ict.ycwl.pathcalculate.pojo.LngAndLat();
                lngLat.setLongitude(point.getLongitude());
                lngLat.setLatitude(point.getLatitude());
                polyline.add(lngLat);
            }
            resultRoute.setPolyline(polyline);
        }

        // 转换凸包
        if (algorithmRoute.getConvexHull() != null) {
            List<com.ict.ycwl.pathcalculate.pojo.LngAndLat> convex = new ArrayList<>();
            for (com.ict.ycwl.pathcalculate.algorithm.entity.CoordinatePoint point : algorithmRoute.getConvexHull()) {
                com.ict.ycwl.pathcalculate.pojo.LngAndLat lngLat =
                        new com.ict.ycwl.pathcalculate.pojo.LngAndLat();
                lngLat.setLongitude(point.getLongitude());
                lngLat.setLatitude(point.getLatitude());
                convex.add(lngLat);
            }
            resultRoute.setConvex(convex);
        }

        // 设置其他字段
        resultRoute.setCreateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        resultRoute.setUpdateTime(new java.sql.Timestamp(System.currentTimeMillis()));
        resultRoute.setDelete(false);

        // 查询并设置groupId和areaId
        try {
            setAdditionalFields(resultRoute);
        } catch (Exception e) {
            log.warn("设置附加字段失败: {}", e.getMessage());
        }

        return resultRoute;
    }

    /**
     * 设置附加字段（groupId、areaId等）
     */
    private void setAdditionalFields(com.ict.ycwl.pathcalculate.pojo.ResultRoute resultRoute) {
        if (resultRoute.getTransitDepotId() == null) {
            return;
        }

        try {
            String sql = "SELECT group_id, area_id FROM transit_depot WHERE transit_depot_id = ? AND is_delete = 0";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, resultRoute.getTransitDepotId());

            if (!results.isEmpty()) {
                Map<String, Object> row = results.get(0);
                if (row.get("group_id") != null) {
                    resultRoute.setGroupId(((Number) row.get("group_id")).longValue());
                }
                if (row.get("area_id") != null) {
                    resultRoute.setAreaId(((Number) row.get("area_id")).longValue());
                }
            }
        } catch (Exception e) {
            log.warn("查询中转站附加信息失败: {}", e.getMessage());
        }
    }
}
