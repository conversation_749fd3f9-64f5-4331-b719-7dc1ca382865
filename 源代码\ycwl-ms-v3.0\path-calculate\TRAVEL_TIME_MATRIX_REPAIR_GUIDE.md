# 时间矩阵数据修复指南

## 🎯 问题背景

路径规划算法要求时间矩阵覆盖度达到80%以上，但当前数据库中的`travel_time`表覆盖度仅为1.3%，导致算法验证失败。

### 问题根源
- **数据不完整**：缺少大量中转站内部的点对点时间数据
- **坐标格式不匹配**：travel_time表使用varchar存储坐标，与业务表的double类型不一致
- **生成机制缺失**：没有系统化的时间矩阵数据生成流程

## 🔧 解决方案

### 1. 新增组件

#### TravelTimeMapper
- **位置**: `com.ict.ycwl.pathcalculate.mapper.TravelTimeMapper`
- **功能**: 专门处理travel_time表的CRUD操作
- **特性**: 支持批量插入、覆盖度统计、坐标查询

#### AmapApiService  
- **位置**: `com.ict.ycwl.pathcalculate.service.AmapApiService`
- **功能**: 封装高德地图路径规划API
- **特性**: 自动重试、频率限制、降级估算

#### TravelTimeGeneratorService
- **位置**: `com.ict.ycwl.pathcalculate.service.TravelTimeGeneratorService`
- **功能**: 按中转站生成完整时间矩阵
- **特性**: 批量处理、进度跟踪、异常恢复

#### TravelTimeController
- **位置**: `com.ict.ycwl.pathcalculate.controller.TravelTimeController`
- **功能**: 提供数据修复的API接口
- **特性**: 异步执行、状态查询、健康检查

## 🚀 使用方法

### 方法1: API接口调用

#### 1. 启动服务
```bash
cd 源代码/ycwl-ms-v3.0/path-calculate
mvn spring-boot:run
```

#### 2. 生成时间矩阵数据
```bash
# 增量生成（跳过已存在的数据）
curl -X POST "http://localhost:8084/travel-time/generate?forceRegenerate=false"

# 强制重新生成（删除现有数据）
curl -X POST "http://localhost:8084/travel-time/generate?forceRegenerate=true"
```

#### 3. 检查生成状态
```bash
# 查看统计信息
curl "http://localhost:8084/travel-time/stats"

# 检查覆盖度
curl "http://localhost:8084/travel-time/coverage"

# 健康检查
curl "http://localhost:8084/travel-time/health"
```

### 方法2: 直接调用服务

```java
@Autowired
private TravelTimeGeneratorService travelTimeGeneratorService;

// 生成所有中转站的时间矩阵
GenerationResult result = travelTimeGeneratorService.generateAllTravelTimeMatrix(false);

System.out.println("生成完成: " + result.getTotalGenerated() + " 条记录");
```

## 📊 数据规模预估

### 当前数据分布
| 中转站ID | 聚集区数量 | 总点数 | 期望记录数 | 预估API调用 |
|---------|-----------|--------|-----------|------------|
| 5 | 497 | 498 | 247,506 | 247,506 |
| 6 | 473 | 474 | 224,202 | 224,202 |
| 4 | 328 | 329 | 107,912 | 107,912 |
| 2 | 237 | 238 | 56,406 | 56,406 |
| 3 | 168 | 169 | 28,392 | 28,392 |
| 1 | 118 | 119 | 14,042 | 14,042 |
| **总计** | **1,821** | **1,827** | **678,460** | **678,460** |

### 时间和成本预估
- **API调用次数**: 约67.8万次
- **预估耗时**: 19-38小时（考虑100ms间隔限流）
- **高德API成本**: 根据你的套餐计算

## ⚠️ 注意事项

### 1. API配额管理
- 确保高德地图API配额充足（需要67.8万次调用）
- 建议分批执行，避免一次性消耗完配额
- 可以先测试小规模中转站（如中转站1，仅需1.4万次调用）

### 2. 执行策略
```bash
# 建议执行顺序：
# 1. 先测试最小的中转站
curl -X POST "http://localhost:8084/travel-time/generate?forceRegenerate=false"

# 2. 检查结果和覆盖度
curl "http://localhost:8084/travel-time/coverage"

# 3. 验证算法是否能正常运行
curl "http://localhost:8084/path/calculateAll?apiKey=3729e38b382749ba3a10bae7539e0d9a"
```

### 3. 监控和恢复
- 生成过程中可能因网络问题中断，支持断点续传
- 使用`forceRegenerate=false`可以跳过已生成的数据
- 查看日志监控进度和错误信息

### 4. 数据验证
生成完成后，验证数据质量：
```sql
-- 检查总记录数
SELECT COUNT(*) FROM travel_time;

-- 检查数据有效性
SELECT COUNT(*) FROM travel_time WHERE travel_time IS NULL OR travel_time <= 0;

-- 检查坐标格式
SELECT longitude_start, latitude_start FROM travel_time LIMIT 5;
```

## 🧪 测试验证

### 1. 运行单元测试
```bash
mvn test -Dtest=TravelTimeGeneratorTest
```

### 2. API连接测试
```bash
curl "http://localhost:8084/travel-time/test-api"
```

### 3. 算法验证测试
```bash
# 生成数据后测试算法
curl "http://localhost:8084/path/calculateAll?apiKey=3729e38b382749ba3a10bae7539e0d9a"
```

## 🔍 故障排除

### 常见问题

#### 1. API调用失败
- 检查网络连接
- 验证高德API密钥是否有效
- 确认API配额是否充足

#### 2. 数据库连接问题
- 检查数据库连接配置
- 确认数据库服务是否正常
- 验证表结构是否正确

#### 3. 内存不足
- 调整JVM堆内存大小
- 减少批量处理大小（BATCH_SIZE）
- 分批处理不同中转站

### 日志查看
```bash
# 查看生成进度
tail -f logs/path-calculate.log | grep "TravelTimeGenerator"

# 查看API调用情况
tail -f logs/path-calculate.log | grep "AmapApi"
```

## 📈 性能优化

### 1. 并发处理
当前实现为单线程顺序处理，可以考虑：
- 多线程并发调用API（注意频率限制）
- 按中转站并行处理
- 使用连接池优化数据库操作

### 2. 缓存策略
- 缓存API响应结果
- 使用Redis缓存热点数据
- 实现本地文件缓存

### 3. 批量优化
- 调整批量插入大小
- 使用事务批量提交
- 优化SQL语句性能

## 🎯 预期效果

修复完成后：
- ✅ 时间矩阵覆盖度从1.3%提升到80%+
- ✅ 路径规划算法正常运行
- ✅ 生成高质量的配送路线
- ✅ 系统具备完整的数据生成能力

---

**重要提醒**: 请在生产环境执行前，先在测试环境验证整个流程！
