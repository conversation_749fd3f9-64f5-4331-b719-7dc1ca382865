package com.ict.ycwl.pathcalculate.converter;

import com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 坐标对齐功能测试
 * 测试修复后的数据转换器是否能正确处理坐标对齐问题
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@SpringBootTest(properties = {
        "spring.cloud.nacos.config.enabled=false",
        "spring.cloud.nacos.discovery.enabled=false"
})
@TestPropertySource(properties = {
        "spring.datasource.url=********************************",
        "spring.datasource.username=root",
        "spring.datasource.password=aA13717028793#",
        "jjking.dbPath=test-path"
})
public class CoordinateAlignmentTest {

    @Autowired
    private PathPlanningDataConverter pathPlanningDataConverter;

    /**
     * 测试坐标对齐功能
     */
    @Test
    public void testCoordinateAlignment() {
        log.info("=== 测试坐标对齐功能 ===");
        
        try {
            // 加载数据并进行坐标对齐
            PathPlanningRequest request = pathPlanningDataConverter.loadPathPlanningRequest();
            
            log.info("数据加载完成:");
            log.info("- 聚集区数量: {}", request.getAccumulations().size());
            log.info("- 中转站数量: {}", request.getTransitDepots().size());
            log.info("- 班组数量: {}", request.getTeams().size());
            log.info("- 时间矩阵记录: {}", request.getTimeMatrix().size());
            
            // 验证数据有效性
            boolean isValid = request.isValid();
            log.info("数据有效性验证: {}", isValid ? "通过" : "失败");
            
            if (!isValid) {
                log.error("数据验证失败，请检查坐标对齐逻辑");
                throw new RuntimeException("数据验证失败");
            }
            
            // 检查时间矩阵覆盖度
            checkTimeMatrixCoverage(request);
            
            log.info("坐标对齐功能测试通过 ✓");
            
        } catch (Exception e) {
            log.error("坐标对齐功能测试失败", e);
            throw new RuntimeException("坐标对齐功能测试失败", e);
        }
    }

    /**
     * 检查时间矩阵覆盖度
     */
    private void checkTimeMatrixCoverage(PathPlanningRequest request) {
        log.info("=== 检查时间矩阵覆盖度 ===");
        
        // 按中转站分组聚集区
        java.util.Map<Long, java.util.List<com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation>> depotAccumulations = new java.util.HashMap<>();
        for (com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation acc : request.getAccumulations()) {
            depotAccumulations.computeIfAbsent(acc.getTransitDepotId(), k -> new java.util.ArrayList<>()).add(acc);
        }
        
        // 构建中转站ID到对象的映射
        java.util.Map<Long, com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot> depotMap = new java.util.HashMap<>();
        for (com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot depot : request.getTransitDepots()) {
            depotMap.put(depot.getTransitDepotId(), depot);
        }
        
        int totalExpectedPairs = 0;
        int foundPairs = 0;
        
        // 按中转站检查覆盖度
        for (java.util.Map.Entry<Long, java.util.List<com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation>> entry : depotAccumulations.entrySet()) {
            Long depotId = entry.getKey();
            java.util.List<com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation> accumulations = entry.getValue();
            com.ict.ycwl.pathcalculate.algorithm.entity.TransitDepot depot = depotMap.get(depotId);
            
            if (depot == null) {
                continue;
            }
            
            // 该中转站内的所有坐标点（中转站 + 聚集区）
            java.util.List<String> depotCoordinates = new java.util.ArrayList<>();
            
            // 添加中转站坐标
            String depotCoord = String.format("%.6f,%.6f", depot.getLongitude(), depot.getLatitude());
            depotCoordinates.add(depotCoord);
            
            // 添加该中转站的聚集区坐标
            for (com.ict.ycwl.pathcalculate.algorithm.entity.Accumulation acc : accumulations) {
                String accCoord = String.format("%.6f,%.6f", acc.getLongitude(), acc.getLatitude());
                depotCoordinates.add(accCoord);
            }
            
            // 计算该中转站内的期望点对数（不包括自环）
            int depotExpectedPairs = depotCoordinates.size() * (depotCoordinates.size() - 1);
            totalExpectedPairs += depotExpectedPairs;
            
            // 检查该中转站内的时间矩阵覆盖
            int depotFoundPairs = 0;
            for (String from : depotCoordinates) {
                for (String to : depotCoordinates) {
                    if (!from.equals(to)) {
                        String key = from + "->" + to;
                        if (request.getTimeMatrix().containsKey(key)) {
                            depotFoundPairs++;
                            foundPairs++;
                        }
                    }
                }
            }
            
            // 记录每个中转站的覆盖情况
            double depotCoverage = (double) depotFoundPairs / depotExpectedPairs;
            log.info("中转站{}覆盖度: {}% ({}/{})", 
                depotId, String.format("%.1f", depotCoverage * 100), depotFoundPairs, depotExpectedPairs);
        }
        
        double coverage = (double) foundPairs / totalExpectedPairs;
        log.info("总体时间矩阵覆盖度: {}% ({}/{})", String.format("%.1f", coverage * 100), foundPairs, totalExpectedPairs);
        
        if (coverage >= 0.8) {
            log.info("时间矩阵覆盖度达标 ✓");
        } else {
            log.warn("时间矩阵覆盖度仍然不足: {}%，需要进一步优化", String.format("%.1f", coverage * 100));
        }
    }

    /**
     * 测试算法调用
     */
    @Test
    public void testAlgorithmExecution() {
        log.info("=== 测试算法调用 ===");
        
        try {
            // 加载数据
            PathPlanningRequest request = pathPlanningDataConverter.loadPathPlanningRequest();
            
            // 调用算法
            com.ict.ycwl.pathcalculate.algorithm.dto.PathPlanningResult result = 
                    com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtils.calculate(request);
            
            // 验证结果
            if (result != null && result.isSuccess()) {
                log.info("算法执行成功 ✓");
                log.info("- 生成路线数: {}", result.getRoutes().size());
                log.info("- 执行时间: {}ms", result.getExecutionTime());
            } else {
                String errorMsg = result != null ? result.getErrorMessage() : "结果为null";
                log.error("算法执行失败: {}", errorMsg);
                throw new RuntimeException("算法执行失败: " + errorMsg);
            }
            
        } catch (Exception e) {
            log.error("算法调用测试失败", e);
            throw new RuntimeException("算法调用测试失败", e);
        }
    }
}
