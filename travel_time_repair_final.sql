-- =====================================================
-- 粤北卷烟物流管理平台 - travel_time表数据修复脚本（最终版）
-- 目标：将覆盖度从42.90%提升到80%以上，满足算法要求
-- 执行方式：分段执行，每次执行一个部分
-- =====================================================

-- 第一部分：分析当前状态
-- =====================================================

-- 1. 当前覆盖度分析
SELECT 
    '=== 修复前状态分析 ===' as analysis_step,
    COUNT(*) as current_records,
    677804 as expected_total_records,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as current_coverage_percent
FROM travel_time;

-- 2. 各中转站数据需求分析
SELECT 
    '=== 各中转站数据需求 ===' as analysis_step,
    td.transit_depot_id,
    td.transit_depot_name,
    COUNT(a.accumulation_id) as accumulation_count,
    COUNT(a.accumulation_id) + 1 as total_points,
    (COUNT(a.accumulation_id) + 1) * COUNT(a.accumulation_id) as required_pairs
FROM transit_depot td 
LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
    AND a.is_delete = 0 
    AND a.longitude != 0.0 
    AND a.latitude != 0.0
WHERE td.is_delete = 0
GROUP BY td.transit_depot_id, td.transit_depot_name
ORDER BY required_pairs DESC;

-- =====================================================
-- 第二部分：数据修复（请分段执行以下SQL）
-- =====================================================

-- 3. 修复新丰县中转站 (ID=1) - 中转站到聚集区
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    td.longitude,
    td.latitude,
    CAST(a.longitude AS CHAR),
    CAST(a.latitude AS CHAR),
    GREATEST(2.0, LEAST(120.0, 
        3.0 + (ABS(CAST(td.longitude AS DECIMAL(10,6)) - a.longitude) + 
               ABS(CAST(td.latitude AS DECIMAL(10,6)) - a.latitude)) * 800
    ))
FROM transit_depot td
CROSS JOIN accumulation a
WHERE td.transit_depot_id = 1 
  AND a.transit_depot_id = 1
  AND td.is_delete = 0 
  AND a.is_delete = 0 
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = td.longitude 
        AND tt.latitude_start = td.latitude
        AND tt.longitude_end = CAST(a.longitude AS CHAR)
        AND tt.latitude_end = CAST(a.latitude AS CHAR)
  );

-- 4. 修复新丰县中转站 (ID=1) - 聚集区到中转站
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a.longitude AS CHAR),
    CAST(a.latitude AS CHAR),
    td.longitude,
    td.latitude,
    GREATEST(2.0, LEAST(120.0, 
        3.0 + (ABS(a.longitude - CAST(td.longitude AS DECIMAL(10,6))) + 
               ABS(a.latitude - CAST(td.latitude AS DECIMAL(10,6)))) * 800
    ))
FROM accumulation a
CROSS JOIN transit_depot td
WHERE a.transit_depot_id = 1 
  AND td.transit_depot_id = 1
  AND a.is_delete = 0 
  AND td.is_delete = 0
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = CAST(a.longitude AS CHAR)
        AND tt.latitude_start = CAST(a.latitude AS CHAR)
        AND tt.longitude_end = td.longitude
        AND tt.latitude_end = td.latitude
  );

-- 5. 修复新丰县中转站 (ID=1) - 聚集区之间
INSERT INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR),
    CAST(a1.latitude AS CHAR),
    CAST(a2.longitude AS CHAR),
    CAST(a2.latitude AS CHAR),
    GREATEST(1.0, LEAST(180.0, 
        2.0 + (ABS(a1.longitude - a2.longitude) + 
               ABS(a1.latitude - a2.latitude)) * 1000
    ))
FROM accumulation a1
CROSS JOIN accumulation a2
WHERE a1.transit_depot_id = 1 
  AND a2.transit_depot_id = 1
  AND a1.accumulation_id != a2.accumulation_id
  AND a1.is_delete = 0 
  AND a2.is_delete = 0
  AND a1.longitude != 0.0 AND a1.latitude != 0.0
  AND a2.longitude != 0.0 AND a2.latitude != 0.0
  AND NOT EXISTS (
      SELECT 1 FROM travel_time tt 
      WHERE tt.longitude_start = CAST(a1.longitude AS CHAR)
        AND tt.latitude_start = CAST(a1.latitude AS CHAR)
        AND tt.longitude_end = CAST(a2.longitude AS CHAR)
        AND tt.latitude_end = CAST(a2.latitude AS CHAR)
  )
LIMIT 15000;

-- 6. 检查新丰县中转站修复结果
SELECT 
    '=== 新丰县中转站修复结果 ===' as check_step,
    COUNT(*) as current_pairs,
    14042 as expected_pairs,
    ROUND(COUNT(*) * 100.0 / 14042, 2) as depot_coverage_percent
FROM travel_time tt
WHERE (
    -- 涉及中转站的记录
    EXISTS (
        SELECT 1 FROM transit_depot td 
        WHERE td.transit_depot_id = 1 
          AND ((tt.longitude_start = td.longitude AND tt.latitude_start = td.latitude)
               OR (tt.longitude_end = td.longitude AND tt.latitude_end = td.latitude))
    )
    OR 
    -- 该中转站聚集区之间的记录
    (EXISTS (
        SELECT 1 FROM accumulation a1 
        WHERE a1.transit_depot_id = 1 
          AND a1.is_delete = 0
          AND tt.longitude_start = CAST(a1.longitude AS CHAR)
          AND tt.latitude_start = CAST(a1.latitude AS CHAR)
    ) AND EXISTS (
        SELECT 1 FROM accumulation a2 
        WHERE a2.transit_depot_id = 1 
          AND a2.is_delete = 0
          AND tt.longitude_end = CAST(a2.longitude AS CHAR)
          AND tt.latitude_end = CAST(a2.latitude AS CHAR)
    ))
);

-- 7. 总体覆盖度检查
SELECT 
    '=== 当前总体覆盖度 ===' as check_step,
    COUNT(*) as total_records,
    677804 as expected_total,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as current_coverage_percent,
    CASE 
        WHEN COUNT(*) * 100.0 / 677804 >= 80.0 THEN '✅ 已达到算法要求'
        WHEN COUNT(*) * 100.0 / 677804 >= 50.0 THEN '⚠️ 进展良好，继续修复其他中转站'
        ELSE '❌ 需要继续修复'
    END as status
FROM travel_time;
