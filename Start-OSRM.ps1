# PowerShell script to start OSRM service
Write-Host "========================================" -ForegroundColor Green
Write-Host "Starting OSRM Service" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

# Check if Docker is available
try {
    docker --version | Out-Null
    Write-Host "✓ Docker is available" -ForegroundColor Green
} catch {
    Write-Host "✗ Docker not found. Please install Docker Desktop." -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

# Stop and remove existing container
Write-Host "Stopping existing OSRM container..." -ForegroundColor Yellow
docker stop osrm-backend 2>$null
docker rm osrm-backend 2>$null

# Start OSRM container
Write-Host "Starting OSRM container..." -ForegroundColor Yellow
docker run -d --name osrm-backend -p 5000:5000 osrm/osrm-backend

# Wait for service to start
Write-Host "Waiting for service to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Test the service
Write-Host "Testing OSRM service..." -ForegroundColor Yellow
try {
    $response = Invoke-WebRequest -Uri "http://localhost:5000/route/v1/driving/13.388860,52.517037;13.397634,52.529407?overview=false" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ OSRM service is running successfully!" -ForegroundColor Green
        Write-Host "Service URL: http://localhost:5000" -ForegroundColor Cyan
        Write-Host ""
        Write-Host "Now you can:" -ForegroundColor Yellow
        Write-Host "1. Restart your Java service" -ForegroundColor White
        Write-Host "2. Test the legacy method" -ForegroundColor White
        Write-Host ""
        Write-Host "To stop OSRM: docker stop osrm-backend" -ForegroundColor Gray
    } else {
        throw "Service returned status code: $($response.StatusCode)"
    }
} catch {
    Write-Host "✗ OSRM service failed to start or respond" -ForegroundColor Red
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Checking container logs:" -ForegroundColor Yellow
    docker logs osrm-backend
}

Read-Host "Press Enter to continue"
