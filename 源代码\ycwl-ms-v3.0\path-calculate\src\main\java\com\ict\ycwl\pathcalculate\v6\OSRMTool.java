package com.ict.ycwl.pathcalculate.v6;

import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * OSRM工具类
 * 用于调用OSRM服务获取距离和路径信息
 * 
 * <AUTHOR> Code
 * @version 1.0.0
 */
@Slf4j
public class OSRMTool {
    
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000;
    
    /**
     * 获取距离和高速状态信息
     * 
     * @param url OSRM API URL
     * @return JSON响应对象
     */
    public static Object getDistancesAndHighwayStatus(String url) {
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                log.debug("调用OSRM API (尝试 {}/{}): {}", attempt, MAX_RETRIES, url);
                
                // 创建HTTP连接
                URL urlObj = new URL(url);
                HttpURLConnection connection = (HttpURLConnection) urlObj.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000); // 10秒连接超时
                connection.setReadTimeout(30000);    // 30秒读取超时
                
                // 检查响应码
                int responseCode = connection.getResponseCode();
                if (responseCode != 200) {
                    log.warn("OSRM API返回错误码: {}", responseCode);
                    if (attempt < MAX_RETRIES) {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                        continue;
                    } else {
                        throw new RuntimeException("OSRM API调用失败，响应码: " + responseCode);
                    }
                }
                
                // 读取响应数据
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                connection.disconnect();
                
                // 解析JSON响应
                JSONObject jsonResponse = new JSONObject(response.toString());
                
                // 检查OSRM响应状态
                String code = jsonResponse.optString("code", "");
                if (!"Ok".equals(code)) {
                    log.warn("OSRM返回错误状态: {}", code);
                    if (attempt < MAX_RETRIES) {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                        continue;
                    } else {
                        throw new RuntimeException("OSRM返回错误状态: " + code);
                    }
                }
                
                log.debug("OSRM API调用成功");
                return jsonResponse;
                
            } catch (Exception e) {
                log.warn("OSRM API调用失败 (尝试 {}/{}): {}", attempt, MAX_RETRIES, e.getMessage());
                
                if (attempt < MAX_RETRIES) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                } else {
                    log.error("OSRM API调用最终失败，所有重试都已用完");
                    throw new RuntimeException("OSRM API调用失败: " + e.getMessage(), e);
                }
            }
        }
        
        throw new RuntimeException("OSRM API调用失败，已达到最大重试次数");
    }
    
    /**
     * 检查OSRM服务是否可用
     * 
     * @param baseUrl OSRM服务基础URL (例如: http://192.168.79.130:5000)
     * @return 是否可用
     */
    public static boolean isOSRMServiceAvailable(String baseUrl) {
        try {
            // 使用简单的坐标测试服务可用性
            String testUrl = baseUrl + "/table/v1/driving/113.264385,23.129163;113.280637,23.125178?annotations=distance";
            
            URL url = new URL(testUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(5000);  // 5秒连接超时
            connection.setReadTimeout(10000);    // 10秒读取超时
            
            int responseCode = connection.getResponseCode();
            connection.disconnect();
            
            boolean available = (responseCode == 200);
            log.info("OSRM服务可用性检查: {} (响应码: {})", available ? "可用" : "不可用", responseCode);
            return available;
            
        } catch (Exception e) {
            log.warn("OSRM服务可用性检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 构建OSRM Table API URL
     * 
     * @param baseUrl 基础URL
     * @param coordinates 坐标字符串 (格式: "lng1,lat1;lng2,lat2;...")
     * @return 完整的API URL
     */
    public static String buildTableUrl(String baseUrl, String coordinates) {
        return baseUrl + "/table/v1/driving/" + coordinates + "?annotations=distance";
    }
    
    /**
     * 构建OSRM Route API URL
     * 
     * @param baseUrl 基础URL
     * @param coordinates 坐标字符串 (格式: "lng1,lat1;lng2,lat2")
     * @return 完整的API URL
     */
    public static String buildRouteUrl(String baseUrl, String coordinates) {
        return baseUrl + "/route/v1/driving/" + coordinates + "?overview=false&geometries=geojson";
    }
}
