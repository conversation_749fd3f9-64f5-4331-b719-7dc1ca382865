@echo off
echo ========================================
echo 启动OSRM服务 - 使用Monaco示例数据
echo ========================================

echo 1. 检查Docker是否运行...
docker --version
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动
    echo 请先安装Docker Desktop: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

echo 2. 停止并清理旧容器...
docker stop osrm-backend 2>nul
docker rm osrm-backend 2>nul

echo 3. 下载Monaco示例数据...
if not exist "monaco-latest.osm.pbf" (
    echo 正在下载Monaco示例数据 (小文件，快速下载)...
    curl -L -o monaco-latest.osm.pbf "http://download.geofabrik.de/europe/monaco-latest.osm.pbf"
    if %errorlevel% neq 0 (
        echo 下载失败，尝试备用链接...
        curl -L -o monaco-latest.osm.pbf "https://download.geofabrik.de/europe/monaco-latest.osm.pbf"
    )
)

if not exist "monaco-latest.osm.pbf" (
    echo 下载失败，创建最小测试数据...
    goto create_minimal_data
)

echo 4. 预处理数据...
echo 提取路网...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-extract -p /opt/car.lua /data/monaco-latest.osm.pbf

echo 分区处理...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-partition /data/monaco-latest.osrm

echo 自定义处理...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-customize /data/monaco-latest.osrm

echo 5. 启动OSRM服务...
echo OSRM服务将在 http://localhost:5000 启动
echo 按 Ctrl+C 停止服务
docker run --rm -t -i -p 5000:5000 -v "%cd%:/data" osrm/osrm-backend osrm-routed --algorithm mld /data/monaco-latest.osrm
goto end

:create_minimal_data
echo 创建最小测试数据...
mkdir osrm_data 2>nul
cd osrm_data

echo ^<?xml version="1.0" encoding="UTF-8"?^> > minimal.osm
echo ^<osm version="0.6" generator="test"^> >> minimal.osm
echo ^<node id="1" lat="43.7384" lon="7.4246"/^> >> minimal.osm
echo ^<node id="2" lat="43.7400" lon="7.4260"/^> >> minimal.osm
echo ^<way id="1"^> >> minimal.osm
echo ^<nd ref="1"/^> >> minimal.osm
echo ^<nd ref="2"/^> >> minimal.osm
echo ^<tag k="highway" v="primary"/^> >> minimal.osm
echo ^</way^> >> minimal.osm
echo ^</osm^> >> minimal.osm

echo 预处理最小数据...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-extract -p /opt/car.lua /data/minimal.osm
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-partition /data/minimal.osrm
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-customize /data/minimal.osrm

echo 启动OSRM服务 (最小数据)...
docker run --rm -t -i -p 5000:5000 -v "%cd%:/data" osrm/osrm-backend osrm-routed --algorithm mld /data/minimal.osrm

:end
echo.
echo 测试OSRM服务:
echo curl "http://localhost:5000/route/v1/driving/7.4246,43.7384;7.4260,43.7400?overview=false"
pause
