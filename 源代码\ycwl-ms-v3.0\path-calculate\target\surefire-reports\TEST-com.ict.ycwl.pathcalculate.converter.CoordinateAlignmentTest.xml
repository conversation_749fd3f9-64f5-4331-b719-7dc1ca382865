<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report.xsd" name="com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest" time="9.113" tests="1" errors="1" skipped="0" failures="0">
  <properties>
    <property name="sun.desktop" value="windows"/>
    <property name="awt.toolkit" value="sun.awt.windows.WToolkit"/>
    <property name="file.encoding.pkg" value="sun.io"/>
    <property name="java.specification.version" value="1.8"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\test-classes;C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes;D:\repository\org\springframework\boot\spring-boot-starter-web\2.3.9.RELEASE\spring-boot-starter-web-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter\2.3.9.RELEASE\spring-boot-starter-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot\2.3.9.RELEASE\spring-boot-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-logging\2.3.9.RELEASE\spring-boot-starter-logging-2.3.9.RELEASE.jar;D:\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\repository\org\springframework\boot\spring-boot-starter-json\2.3.9.RELEASE\spring-boot-starter-json-2.3.9.RELEASE.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.9.RELEASE\spring-boot-starter-tomcat-2.3.9.RELEASE.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.43\tomcat-embed-core-9.0.43.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.43\tomcat-embed-websocket-9.0.43.jar;D:\repository\org\springframework\spring-web\5.2.13.RELEASE\spring-web-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-beans\5.2.13.RELEASE\spring-beans-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-webmvc\5.2.13.RELEASE\spring-webmvc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-aop\5.2.13.RELEASE\spring-aop-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-context\5.2.13.RELEASE\spring-context-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-expression\5.2.13.RELEASE\spring-expression-5.2.13.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-test\2.3.9.RELEASE\spring-boot-starter-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test\2.3.9.RELEASE\spring-boot-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.9.RELEASE\spring-boot-test-autoconfigure-2.3.9.RELEASE.jar;D:\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;D:\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;D:\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\repository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\repository\net\bytebuddy\byte-buddy\1.10.20\byte-buddy-1.10.20.jar;D:\repository\net\bytebuddy\byte-buddy-agent\1.10.20\byte-buddy-agent-1.10.20.jar;D:\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\repository\org\springframework\spring-core\5.2.13.RELEASE\spring-core-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-jcl\5.2.13.RELEASE\spring-jcl-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-test\5.2.13.RELEASE\spring-test-5.2.13.RELEASE.jar;D:\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\repository\junit\junit\4.13.2\junit-4.13.2.jar;D:\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;D:\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;D:\repository\commons-codec\commons-codec\1.14\commons-codec-1.14.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-starter\4.3.0\dynamic-datasource-spring-boot-starter-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-common\4.3.0\dynamic-datasource-spring-boot-common-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring\4.3.0\dynamic-datasource-spring-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-creator\4.3.0\dynamic-datasource-creator-4.3.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-aop\2.3.9.RELEASE\spring-boot-starter-aop-2.3.9.RELEASE.jar;D:\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.2.6.RELEASE.jar;D:\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2.2.6.RELEASE\spring-cloud-alibaba-commons-2.2.6.RELEASE.jar;D:\repository\com\alibaba\nacos\nacos-client\1.4.2\nacos-client-1.4.2.jar;D:\repository\com\alibaba\nacos\nacos-common\1.4.2\nacos-common-1.4.2.jar;D:\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;D:\repository\org\apache\httpcomponents\httpcore-nio\4.4.14\httpcore-nio-4.4.14.jar;D:\repository\com\alibaba\nacos\nacos-api\1.4.2\nacos-api-1.4.2.jar;D:\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;D:\repository\com\alibaba\spring\spring-context-support\1.0.10\spring-context-support-1.0.10.jar;D:\repository\org\springframework\cloud\spring-cloud-commons\2.2.7.RELEASE\spring-cloud-commons-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-crypto\5.3.8.RELEASE\spring-security-crypto-5.3.8.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-context\2.2.7.RELEASE\spring-cloud-context-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.2.7.RELEASE\spring-cloud-starter-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter\2.2.7.RELEASE\spring-cloud-starter-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-rsa\1.0.9.RELEASE\spring-security-rsa-1.0.9.RELEASE.jar;D:\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;D:\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.2.7.RELEASE\spring-cloud-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.2.7.RELEASE\spring-cloud-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.2.7.RELEASE\spring-cloud-starter-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\com\netflix\archaius\archaius-core\0.7.7\archaius-core-0.7.7.jar;D:\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;D:\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;D:\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;D:\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;D:\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;D:\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;D:\repository\javax\inject\javax.inject\1\javax.inject-1.jar;D:\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;D:\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;D:\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;D:\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;D:\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;D:\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;D:\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;D:\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;D:\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;D:\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;D:\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-config-2.2.6.RELEASE.jar;D:\repository\mysql\mysql-connector-java\5.1.47\mysql-connector-java-5.1.47.jar;D:\repository\com\alibaba\druid-spring-boot-starter\1.2.25\druid-spring-boot-starter-1.2.25.jar;D:\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.9.RELEASE\spring-boot-autoconfigure-2.3.9.RELEASE.jar;D:\repository\org\glassfish\jaxb\jaxb-runtime\2.3.3\jaxb-runtime-2.3.3.jar;D:\repository\org\glassfish\jaxb\txw2\2.3.3\txw2-2.3.3.jar;D:\repository\com\sun\istack\istack-commons-runtime\3.0.11\istack-commons-runtime-3.0.11.jar;D:\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.1\mybatis-spring-boot-starter-2.1.1.jar;D:\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.9.RELEASE\spring-boot-starter-jdbc-2.3.9.RELEASE.jar;D:\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\repository\org\springframework\spring-jdbc\5.2.13.RELEASE\spring-jdbc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-tx\5.2.13.RELEASE\spring-tx-5.2.13.RELEASE.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.1\mybatis-spring-boot-autoconfigure-2.1.1.jar;D:\repository\org\mybatis\mybatis\3.5.3\mybatis-3.5.3.jar;D:\repository\org\mybatis\mybatis-spring\2.0.3\mybatis-spring-2.0.3.jar;D:\repository\org\projectlombok\lombok\1.18.18\lombok-1.18.18.jar;D:\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;D:\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;D:\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;D:\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;D:\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;D:\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;D:\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;D:\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;D:\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;D:\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;D:\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;D:\repository\com\github\xiaoymin\knife4j-spring-ui\3.0.2\knife4j-spring-ui-3.0.2.jar;D:\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;D:\repository\cn\hutool\hutool-all\5.8.8\hutool-all-5.8.8.jar;D:\repository\com\ict\ycwl\common\1.0-SNAPSHOT\common-1.0-SNAPSHOT.jar;D:\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;D:\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;D:\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;D:\repository\com\auth0\java-jwt\3.4.0\java-jwt-3.4.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-validation\2.3.2.RELEASE\spring-boot-starter-validation-2.3.2.RELEASE.jar;D:\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\repository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.6\pagehelper-spring-boot-starter-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.6\pagehelper-spring-boot-autoconfigure-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper\5.3.2\pagehelper-5.3.2.jar;D:\repository\com\github\jsqlparser\jsqlparser\4.5\jsqlparser-4.5.jar;D:\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;D:\repository\org\apache\poi\poi\5.2.3\poi-5.2.3.jar;D:\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;D:\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\repository\org\apache\poi\poi-ooxml\5.2.3\poi-ooxml-5.2.3.jar;D:\repository\org\apache\poi\poi-ooxml-lite\5.2.3\poi-ooxml-lite-5.2.3.jar;D:\repository\org\apache\xmlbeans\xmlbeans\5.1.1\xmlbeans-5.1.1.jar;D:\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;D:\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;D:\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;D:\repository\org\locationtech\jts\jts-core\1.19.0\jts-core-1.19.0.jar;D:\repository\org\locationtech\jts\io\jts-io-common\1.19.0\jts-io-common-1.19.0.jar;D:\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\repository\com\alibaba\easyexcel\3.1.1\easyexcel-3.1.1.jar;D:\repository\com\alibaba\easyexcel-core\3.1.1\easyexcel-core-3.1.1.jar;D:\repository\com\alibaba\easyexcel-support\3.1.1\easyexcel-support-3.1.1.jar;D:\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;D:\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\repository\org\ehcache\ehcache\3.8.1\ehcache-3.8.1.jar;D:\repository\com\google\guava\guava\33.4.0-jre\guava-33.4.0-jre.jar;D:\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;D:\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\repository\org\checkerframework\checker-qual\3.43.0\checker-qual-3.43.0.jar;D:\repository\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar;D:\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;D:\repository\com\google\ortools\ortools-java\9.8.3296\ortools-java-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-x86-64\9.8.3296\ortools-linux-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-x86-64\9.8.3296\ortools-darwin-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-aarch64\9.8.3296\ortools-linux-aarch64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-aarch64\9.8.3296\ortools-darwin-aarch64-9.8.3296.jar;D:\repository\net\java\dev\jna\jna-platform\5.13.0\jna-platform-5.13.0.jar;D:\repository\net\java\dev\jna\jna\5.13.0\jna-5.13.0.jar;D:\repository\com\google\protobuf\protobuf-java\3.13.0\protobuf-java-3.13.0.jar;D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar;D:\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\repository\io\jenetics\jenetics\7.2.0\jenetics-7.2.0.jar;D:\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;D:\repository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\repository\org\optaplanner\optaplanner-core\7.73.0.Final\optaplanner-core-7.73.0.Final.jar;D:\repository\org\kie\kie-api\7.73.0.Final\kie-api-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-maven-support\7.73.0.Final\kie-soup-maven-support-7.73.0.Final.jar;D:\repository\org\kie\kie-internal\7.73.0.Final\kie-internal-7.73.0.Final.jar;D:\repository\org\drools\drools-core\7.73.0.Final\drools-core-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-xstream\7.73.0.Final\kie-soup-xstream-7.73.0.Final.jar;D:\repository\org\drools\drools-core-reflective\7.73.0.Final\drools-core-reflective-7.73.0.Final.jar;D:\repository\org\drools\drools-core-dynamic\7.73.0.Final\drools-core-dynamic-7.73.0.Final.jar;D:\repository\org\drools\drools-compiler\7.73.0.Final\drools-compiler-7.73.0.Final.jar;D:\repository\org\kie\kie-memory-compiler\7.73.0.Final\kie-memory-compiler-7.73.0.Final.jar;D:\repository\org\drools\drools-ecj\7.73.0.Final\drools-ecj-7.73.0.Final.jar;D:\repository\org\antlr\antlr-runtime\3.5.2\antlr-runtime-3.5.2.jar;D:\repository\org\drools\drools-mvel\7.73.0.Final\drools-mvel-7.73.0.Final.jar;D:\repository\org\mvel\mvel2\2.4.14.Final\mvel2-2.4.14.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-commons\7.73.0.Final\kie-soup-project-datamodel-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-commons\7.73.0.Final\kie-soup-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-api\7.73.0.Final\kie-soup-project-datamodel-api-7.73.0.Final.jar;D:\repository\org\drools\drools-canonical-model\7.73.0.Final\drools-canonical-model-7.73.0.Final.jar;D:\repository\org\drools\drools-model-compiler\7.73.0.Final\drools-model-compiler-7.73.0.Final.jar;D:\repository\com\github\javaparser\javaparser-core\3.23.1\javaparser-core-3.23.1.jar;D:\repository\org\drools\drools-mvel-parser\7.73.0.Final\drools-mvel-parser-7.73.0.Final.jar;D:\repository\org\drools\drools-mvel-compiler\7.73.0.Final\drools-mvel-compiler-7.73.0.Final.jar;D:\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;D:\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;D:\repository\com\thoughtworks\xstream\xstream\1.4.19\xstream-1.4.19.jar;D:\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-starter\7.73.0.Final\optaplanner-spring-boot-starter-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-autoconfigure\7.73.0.Final\optaplanner-spring-boot-autoconfigure-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-jackson\7.73.0.Final\optaplanner-persistence-jackson-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-common\7.73.0.Final\optaplanner-persistence-common-7.73.0.Final.jar;D:\repository\com\graphhopper\jsprit-core\1.8\jsprit-core-1.8.jar;D:\repository\com\uber\h3\3.7.3\h3-3.7.3.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="http://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Shanghai"/>
    <property name="java.vm.specification.version" value="1.8"/>
    <property name="os.name" value="Windows 10"/>
    <property name="user.country" value="CN"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Java\jdk1.8.0_151\jre\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire7067974933298881386\surefirebooter8688533736419783157.jar C:\Users\<USER>\AppData\Local\Temp\surefire7067974933298881386 2025-08-07T22-58-47_426-jvmRun1 surefire8510367093532218059tmp surefire_07461325476088655790tmp"/>
    <property name="test" value="CoordinateAlignmentTest#testCoordinateAlignment"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\test-classes;C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate\target\classes;D:\repository\org\springframework\boot\spring-boot-starter-web\2.3.9.RELEASE\spring-boot-starter-web-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter\2.3.9.RELEASE\spring-boot-starter-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot\2.3.9.RELEASE\spring-boot-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-logging\2.3.9.RELEASE\spring-boot-starter-logging-2.3.9.RELEASE.jar;D:\repository\ch\qos\logback\logback-classic\1.2.3\logback-classic-1.2.3.jar;D:\repository\ch\qos\logback\logback-core\1.2.3\logback-core-1.2.3.jar;D:\repository\org\apache\logging\log4j\log4j-to-slf4j\2.13.3\log4j-to-slf4j-2.13.3.jar;D:\repository\org\slf4j\jul-to-slf4j\1.7.30\jul-to-slf4j-1.7.30.jar;D:\repository\jakarta\annotation\jakarta.annotation-api\1.3.5\jakarta.annotation-api-1.3.5.jar;D:\repository\org\yaml\snakeyaml\1.26\snakeyaml-1.26.jar;D:\repository\org\springframework\boot\spring-boot-starter-json\2.3.9.RELEASE\spring-boot-starter-json-2.3.9.RELEASE.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.11.4\jackson-datatype-jdk8-2.11.4.jar;D:\repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.11.4\jackson-datatype-jsr310-2.11.4.jar;D:\repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.11.4\jackson-module-parameter-names-2.11.4.jar;D:\repository\org\springframework\boot\spring-boot-starter-tomcat\2.3.9.RELEASE\spring-boot-starter-tomcat-2.3.9.RELEASE.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-core\9.0.43\tomcat-embed-core-9.0.43.jar;D:\repository\org\apache\tomcat\embed\tomcat-embed-websocket\9.0.43\tomcat-embed-websocket-9.0.43.jar;D:\repository\org\springframework\spring-web\5.2.13.RELEASE\spring-web-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-beans\5.2.13.RELEASE\spring-beans-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-webmvc\5.2.13.RELEASE\spring-webmvc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-aop\5.2.13.RELEASE\spring-aop-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-context\5.2.13.RELEASE\spring-context-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-expression\5.2.13.RELEASE\spring-expression-5.2.13.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-starter-test\2.3.9.RELEASE\spring-boot-starter-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test\2.3.9.RELEASE\spring-boot-test-2.3.9.RELEASE.jar;D:\repository\org\springframework\boot\spring-boot-test-autoconfigure\2.3.9.RELEASE\spring-boot-test-autoconfigure-2.3.9.RELEASE.jar;D:\repository\com\jayway\jsonpath\json-path\2.4.0\json-path-2.4.0.jar;D:\repository\net\minidev\json-smart\2.3\json-smart-2.3.jar;D:\repository\net\minidev\accessors-smart\1.2\accessors-smart-1.2.jar;D:\repository\org\ow2\asm\asm\5.0.4\asm-5.0.4.jar;D:\repository\jakarta\xml\bind\jakarta.xml.bind-api\2.3.3\jakarta.xml.bind-api-2.3.3.jar;D:\repository\jakarta\activation\jakarta.activation-api\1.2.2\jakarta.activation-api-1.2.2.jar;D:\repository\org\assertj\assertj-core\3.16.1\assertj-core-3.16.1.jar;D:\repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\repository\org\junit\jupiter\junit-jupiter\5.6.3\junit-jupiter-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;D:\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;D:\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-params\5.6.3\junit-jupiter-params-5.6.3.jar;D:\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;D:\repository\org\junit\vintage\junit-vintage-engine\5.6.3\junit-vintage-engine-5.6.3.jar;D:\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;D:\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;D:\repository\org\mockito\mockito-core\3.3.3\mockito-core-3.3.3.jar;D:\repository\net\bytebuddy\byte-buddy\1.10.20\byte-buddy-1.10.20.jar;D:\repository\net\bytebuddy\byte-buddy-agent\1.10.20\byte-buddy-agent-1.10.20.jar;D:\repository\org\objenesis\objenesis\2.6\objenesis-2.6.jar;D:\repository\org\mockito\mockito-junit-jupiter\3.3.3\mockito-junit-jupiter-3.3.3.jar;D:\repository\org\skyscreamer\jsonassert\1.5.0\jsonassert-1.5.0.jar;D:\repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\repository\org\springframework\spring-core\5.2.13.RELEASE\spring-core-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-jcl\5.2.13.RELEASE\spring-jcl-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-test\5.2.13.RELEASE\spring-test-5.2.13.RELEASE.jar;D:\repository\org\xmlunit\xmlunit-core\2.7.0\xmlunit-core-2.7.0.jar;D:\repository\junit\junit\4.13.2\junit-4.13.2.jar;D:\repository\org\hamcrest\hamcrest-core\2.2\hamcrest-core-2.2.jar;D:\repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\repository\org\apache\httpcomponents\httpcore\4.4.14\httpcore-4.4.14.jar;D:\repository\commons-codec\commons-codec\1.14\commons-codec-1.14.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-starter\4.3.0\dynamic-datasource-spring-boot-starter-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring-boot-common\4.3.0\dynamic-datasource-spring-boot-common-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-spring\4.3.0\dynamic-datasource-spring-4.3.0.jar;D:\repository\com\baomidou\dynamic-datasource-creator\4.3.0\dynamic-datasource-creator-4.3.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-aop\2.3.9.RELEASE\spring-boot-starter-aop-2.3.9.RELEASE.jar;D:\repository\org\aspectj\aspectjweaver\1.9.6\aspectjweaver-1.9.6.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-discovery\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-discovery-2.2.6.RELEASE.jar;D:\repository\com\alibaba\cloud\spring-cloud-alibaba-commons\2.2.6.RELEASE\spring-cloud-alibaba-commons-2.2.6.RELEASE.jar;D:\repository\com\alibaba\nacos\nacos-client\1.4.2\nacos-client-1.4.2.jar;D:\repository\com\alibaba\nacos\nacos-common\1.4.2\nacos-common-1.4.2.jar;D:\repository\org\apache\httpcomponents\httpasyncclient\4.1.4\httpasyncclient-4.1.4.jar;D:\repository\org\apache\httpcomponents\httpcore-nio\4.4.14\httpcore-nio-4.4.14.jar;D:\repository\com\alibaba\nacos\nacos-api\1.4.2\nacos-api-1.4.2.jar;D:\repository\io\prometheus\simpleclient\0.5.0\simpleclient-0.5.0.jar;D:\repository\com\alibaba\spring\spring-context-support\1.0.10\spring-context-support-1.0.10.jar;D:\repository\org\springframework\cloud\spring-cloud-commons\2.2.7.RELEASE\spring-cloud-commons-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-crypto\5.3.8.RELEASE\spring-security-crypto-5.3.8.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-context\2.2.7.RELEASE\spring-cloud-context-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-ribbon\2.2.7.RELEASE\spring-cloud-starter-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter\2.2.7.RELEASE\spring-cloud-starter-2.2.7.RELEASE.jar;D:\repository\org\springframework\security\spring-security-rsa\1.0.9.RELEASE\spring-security-rsa-1.0.9.RELEASE.jar;D:\repository\org\bouncycastle\bcpkix-jdk15on\1.64\bcpkix-jdk15on-1.64.jar;D:\repository\org\bouncycastle\bcprov-jdk15on\1.64\bcprov-jdk15on-1.64.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-ribbon\2.2.7.RELEASE\spring-cloud-netflix-ribbon-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-netflix-archaius\2.2.7.RELEASE\spring-cloud-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\org\springframework\cloud\spring-cloud-starter-netflix-archaius\2.2.7.RELEASE\spring-cloud-starter-netflix-archaius-2.2.7.RELEASE.jar;D:\repository\com\netflix\archaius\archaius-core\0.7.7\archaius-core-0.7.7.jar;D:\repository\commons-configuration\commons-configuration\1.8\commons-configuration-1.8.jar;D:\repository\com\netflix\ribbon\ribbon\2.3.0\ribbon-2.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-transport\2.3.0\ribbon-transport-2.3.0.jar;D:\repository\io\reactivex\rxnetty-contexts\0.4.9\rxnetty-contexts-0.4.9.jar;D:\repository\io\reactivex\rxnetty-servo\0.4.9\rxnetty-servo-0.4.9.jar;D:\repository\com\netflix\hystrix\hystrix-core\1.5.18\hystrix-core-1.5.18.jar;D:\repository\org\hdrhistogram\HdrHistogram\2.1.9\HdrHistogram-2.1.9.jar;D:\repository\javax\inject\javax.inject\1\javax.inject-1.jar;D:\repository\io\reactivex\rxnetty\0.4.9\rxnetty-0.4.9.jar;D:\repository\com\netflix\ribbon\ribbon-core\2.3.0\ribbon-core-2.3.0.jar;D:\repository\commons-lang\commons-lang\2.6\commons-lang-2.6.jar;D:\repository\com\netflix\ribbon\ribbon-httpclient\2.3.0\ribbon-httpclient-2.3.0.jar;D:\repository\commons-collections\commons-collections\3.2.2\commons-collections-3.2.2.jar;D:\repository\com\sun\jersey\jersey-client\1.19.1\jersey-client-1.19.1.jar;D:\repository\com\sun\jersey\jersey-core\1.19.1\jersey-core-1.19.1.jar;D:\repository\javax\ws\rs\jsr311-api\1.1.1\jsr311-api-1.1.1.jar;D:\repository\com\sun\jersey\contribs\jersey-apache-client4\1.19.1\jersey-apache-client4-1.19.1.jar;D:\repository\com\netflix\servo\servo-core\0.12.21\servo-core-0.12.21.jar;D:\repository\com\netflix\netflix-commons\netflix-commons-util\0.3.0\netflix-commons-util-0.3.0.jar;D:\repository\com\netflix\ribbon\ribbon-loadbalancer\2.3.0\ribbon-loadbalancer-2.3.0.jar;D:\repository\com\netflix\netflix-commons\netflix-statistics\0.1.1\netflix-statistics-0.1.1.jar;D:\repository\io\reactivex\rxjava\1.3.8\rxjava-1.3.8.jar;D:\repository\com\alibaba\cloud\spring-cloud-starter-alibaba-nacos-config\2.2.6.RELEASE\spring-cloud-starter-alibaba-nacos-config-2.2.6.RELEASE.jar;D:\repository\mysql\mysql-connector-java\5.1.47\mysql-connector-java-5.1.47.jar;D:\repository\com\alibaba\druid-spring-boot-starter\1.2.25\druid-spring-boot-starter-1.2.25.jar;D:\repository\com\alibaba\druid\1.2.25\druid-1.2.25.jar;D:\repository\org\slf4j\slf4j-api\1.7.30\slf4j-api-1.7.30.jar;D:\repository\org\springframework\boot\spring-boot-autoconfigure\2.3.9.RELEASE\spring-boot-autoconfigure-2.3.9.RELEASE.jar;D:\repository\org\glassfish\jaxb\jaxb-runtime\2.3.3\jaxb-runtime-2.3.3.jar;D:\repository\org\glassfish\jaxb\txw2\2.3.3\txw2-2.3.3.jar;D:\repository\com\sun\istack\istack-commons-runtime\3.0.11\istack-commons-runtime-3.0.11.jar;D:\repository\com\sun\activation\jakarta.activation\1.2.2\jakarta.activation-1.2.2.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-starter\2.1.1\mybatis-spring-boot-starter-2.1.1.jar;D:\repository\org\springframework\boot\spring-boot-starter-jdbc\2.3.9.RELEASE\spring-boot-starter-jdbc-2.3.9.RELEASE.jar;D:\repository\com\zaxxer\HikariCP\3.4.5\HikariCP-3.4.5.jar;D:\repository\org\springframework\spring-jdbc\5.2.13.RELEASE\spring-jdbc-5.2.13.RELEASE.jar;D:\repository\org\springframework\spring-tx\5.2.13.RELEASE\spring-tx-5.2.13.RELEASE.jar;D:\repository\org\mybatis\spring\boot\mybatis-spring-boot-autoconfigure\2.1.1\mybatis-spring-boot-autoconfigure-2.1.1.jar;D:\repository\org\mybatis\mybatis\3.5.3\mybatis-3.5.3.jar;D:\repository\org\mybatis\mybatis-spring\2.0.3\mybatis-spring-2.0.3.jar;D:\repository\org\projectlombok\lombok\1.18.18\lombok-1.18.18.jar;D:\repository\io\springfox\springfox-swagger2\2.9.2\springfox-swagger2-2.9.2.jar;D:\repository\io\swagger\swagger-annotations\1.5.20\swagger-annotations-1.5.20.jar;D:\repository\io\swagger\swagger-models\1.5.20\swagger-models-1.5.20.jar;D:\repository\io\springfox\springfox-spi\2.9.2\springfox-spi-2.9.2.jar;D:\repository\io\springfox\springfox-core\2.9.2\springfox-core-2.9.2.jar;D:\repository\io\springfox\springfox-schema\2.9.2\springfox-schema-2.9.2.jar;D:\repository\io\springfox\springfox-swagger-common\2.9.2\springfox-swagger-common-2.9.2.jar;D:\repository\io\springfox\springfox-spring-web\2.9.2\springfox-spring-web-2.9.2.jar;D:\repository\com\fasterxml\classmate\1.5.1\classmate-1.5.1.jar;D:\repository\org\springframework\plugin\spring-plugin-core\1.2.0.RELEASE\spring-plugin-core-1.2.0.RELEASE.jar;D:\repository\org\springframework\plugin\spring-plugin-metadata\1.2.0.RELEASE\spring-plugin-metadata-1.2.0.RELEASE.jar;D:\repository\org\mapstruct\mapstruct\1.2.0.Final\mapstruct-1.2.0.Final.jar;D:\repository\com\github\xiaoymin\knife4j-spring-ui\3.0.2\knife4j-spring-ui-3.0.2.jar;D:\repository\io\springfox\springfox-swagger-ui\2.9.2\springfox-swagger-ui-2.9.2.jar;D:\repository\cn\hutool\hutool-all\5.8.8\hutool-all-5.8.8.jar;D:\repository\com\ict\ycwl\common\1.0-SNAPSHOT\common-1.0-SNAPSHOT.jar;D:\repository\org\apache\commons\commons-lang3\3.10\commons-lang3-3.10.jar;D:\repository\com\github\axet\kaptcha\0.0.9\kaptcha-0.0.9.jar;D:\repository\com\jhlabs\filters\2.0.235\filters-2.0.235.jar;D:\repository\com\auth0\java-jwt\3.4.0\java-jwt-3.4.0.jar;D:\repository\org\springframework\boot\spring-boot-starter-validation\2.3.2.RELEASE\spring-boot-starter-validation-2.3.2.RELEASE.jar;D:\repository\org\glassfish\jakarta.el\3.0.3\jakarta.el-3.0.3.jar;D:\repository\org\hibernate\validator\hibernate-validator\6.1.7.Final\hibernate-validator-6.1.7.Final.jar;D:\repository\jakarta\validation\jakarta.validation-api\2.0.2\jakarta.validation-api-2.0.2.jar;D:\repository\org\jboss\logging\jboss-logging\3.4.1.Final\jboss-logging-3.4.1.Final.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-starter\1.4.6\pagehelper-spring-boot-starter-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper-spring-boot-autoconfigure\1.4.6\pagehelper-spring-boot-autoconfigure-1.4.6.jar;D:\repository\com\github\pagehelper\pagehelper\5.3.2\pagehelper-5.3.2.jar;D:\repository\com\github\jsqlparser\jsqlparser\4.5\jsqlparser-4.5.jar;D:\repository\com\baomidou\mybatis-plus-boot-starter\3.5.1\mybatis-plus-boot-starter-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus\3.5.1\mybatis-plus-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-extension\3.5.1\mybatis-plus-extension-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-core\3.5.1\mybatis-plus-core-3.5.1.jar;D:\repository\com\baomidou\mybatis-plus-annotation\3.5.1\mybatis-plus-annotation-3.5.1.jar;D:\repository\org\apache\poi\poi\5.2.3\poi-5.2.3.jar;D:\repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar;D:\repository\commons-io\commons-io\2.11.0\commons-io-2.11.0.jar;D:\repository\com\zaxxer\SparseBitSet\1.2\SparseBitSet-1.2.jar;D:\repository\org\apache\logging\log4j\log4j-api\2.13.3\log4j-api-2.13.3.jar;D:\repository\org\apache\poi\poi-ooxml\5.2.3\poi-ooxml-5.2.3.jar;D:\repository\org\apache\poi\poi-ooxml-lite\5.2.3\poi-ooxml-lite-5.2.3.jar;D:\repository\org\apache\xmlbeans\xmlbeans\5.1.1\xmlbeans-5.1.1.jar;D:\repository\xml-apis\xml-apis\1.4.01\xml-apis-1.4.01.jar;D:\repository\org\apache\commons\commons-compress\1.21\commons-compress-1.21.jar;D:\repository\com\github\virtuald\curvesapi\1.07\curvesapi-1.07.jar;D:\repository\org\locationtech\jts\jts-core\1.19.0\jts-core-1.19.0.jar;D:\repository\org\locationtech\jts\io\jts-io-common\1.19.0\jts-io-common-1.19.0.jar;D:\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;D:\repository\com\alibaba\easyexcel\3.1.1\easyexcel-3.1.1.jar;D:\repository\com\alibaba\easyexcel-core\3.1.1\easyexcel-core-3.1.1.jar;D:\repository\com\alibaba\easyexcel-support\3.1.1\easyexcel-support-3.1.1.jar;D:\repository\org\apache\poi\poi-ooxml-schemas\4.1.2\poi-ooxml-schemas-4.1.2.jar;D:\repository\org\apache\commons\commons-csv\1.8\commons-csv-1.8.jar;D:\repository\org\ehcache\ehcache\3.8.1\ehcache-3.8.1.jar;D:\repository\com\google\guava\guava\33.4.0-jre\guava-33.4.0-jre.jar;D:\repository\com\google\guava\failureaccess\1.0.2\failureaccess-1.0.2.jar;D:\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;D:\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;D:\repository\org\checkerframework\checker-qual\3.43.0\checker-qual-3.43.0.jar;D:\repository\com\google\errorprone\error_prone_annotations\2.36.0\error_prone_annotations-2.36.0.jar;D:\repository\com\google\j2objc\j2objc-annotations\3.0.0\j2objc-annotations-3.0.0.jar;D:\repository\com\google\ortools\ortools-java\9.8.3296\ortools-java-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-x86-64\9.8.3296\ortools-linux-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-x86-64\9.8.3296\ortools-darwin-x86-64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-linux-aarch64\9.8.3296\ortools-linux-aarch64-9.8.3296.jar;D:\repository\com\google\ortools\ortools-darwin-aarch64\9.8.3296\ortools-darwin-aarch64-9.8.3296.jar;D:\repository\net\java\dev\jna\jna-platform\5.13.0\jna-platform-5.13.0.jar;D:\repository\net\java\dev\jna\jna\5.13.0\jna-5.13.0.jar;D:\repository\com\google\protobuf\protobuf-java\3.13.0\protobuf-java-3.13.0.jar;D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar;D:\repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar;D:\repository\io\jenetics\jenetics\7.2.0\jenetics-7.2.0.jar;D:\repository\com\fasterxml\jackson\core\jackson-databind\2.15.2\jackson-databind-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-core\2.15.2\jackson-core-2.15.2.jar;D:\repository\com\fasterxml\jackson\core\jackson-annotations\2.15.2\jackson-annotations-2.15.2.jar;D:\repository\com\h2database\h2\1.4.200\h2-1.4.200.jar;D:\repository\org\optaplanner\optaplanner-core\7.73.0.Final\optaplanner-core-7.73.0.Final.jar;D:\repository\org\kie\kie-api\7.73.0.Final\kie-api-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-maven-support\7.73.0.Final\kie-soup-maven-support-7.73.0.Final.jar;D:\repository\org\kie\kie-internal\7.73.0.Final\kie-internal-7.73.0.Final.jar;D:\repository\org\drools\drools-core\7.73.0.Final\drools-core-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-xstream\7.73.0.Final\kie-soup-xstream-7.73.0.Final.jar;D:\repository\org\drools\drools-core-reflective\7.73.0.Final\drools-core-reflective-7.73.0.Final.jar;D:\repository\org\drools\drools-core-dynamic\7.73.0.Final\drools-core-dynamic-7.73.0.Final.jar;D:\repository\org\drools\drools-compiler\7.73.0.Final\drools-compiler-7.73.0.Final.jar;D:\repository\org\kie\kie-memory-compiler\7.73.0.Final\kie-memory-compiler-7.73.0.Final.jar;D:\repository\org\drools\drools-ecj\7.73.0.Final\drools-ecj-7.73.0.Final.jar;D:\repository\org\antlr\antlr-runtime\3.5.2\antlr-runtime-3.5.2.jar;D:\repository\org\drools\drools-mvel\7.73.0.Final\drools-mvel-7.73.0.Final.jar;D:\repository\org\mvel\mvel2\2.4.14.Final\mvel2-2.4.14.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-commons\7.73.0.Final\kie-soup-project-datamodel-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-commons\7.73.0.Final\kie-soup-commons-7.73.0.Final.jar;D:\repository\org\kie\soup\kie-soup-project-datamodel-api\7.73.0.Final\kie-soup-project-datamodel-api-7.73.0.Final.jar;D:\repository\org\drools\drools-canonical-model\7.73.0.Final\drools-canonical-model-7.73.0.Final.jar;D:\repository\org\drools\drools-model-compiler\7.73.0.Final\drools-model-compiler-7.73.0.Final.jar;D:\repository\com\github\javaparser\javaparser-core\3.23.1\javaparser-core-3.23.1.jar;D:\repository\org\drools\drools-mvel-parser\7.73.0.Final\drools-mvel-parser-7.73.0.Final.jar;D:\repository\org\drools\drools-mvel-compiler\7.73.0.Final\drools-mvel-compiler-7.73.0.Final.jar;D:\repository\org\reflections\reflections\0.9.11\reflections-0.9.11.jar;D:\repository\org\javassist\javassist\3.21.0-GA\javassist-3.21.0-GA.jar;D:\repository\com\thoughtworks\xstream\xstream\1.4.19\xstream-1.4.19.jar;D:\repository\io\github\x-stream\mxparser\1.2.2\mxparser-1.2.2.jar;D:\repository\xmlpull\xmlpull\1.1.3.1\xmlpull-1.1.3.1.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-starter\7.73.0.Final\optaplanner-spring-boot-starter-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-spring-boot-autoconfigure\7.73.0.Final\optaplanner-spring-boot-autoconfigure-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-jackson\7.73.0.Final\optaplanner-persistence-jackson-7.73.0.Final.jar;D:\repository\org\optaplanner\optaplanner-persistence-common\7.73.0.Final\optaplanner-persistence-common-7.73.0.Final.jar;D:\repository\com\graphhopper\jsprit-core\1.8\jsprit-core-1.8.jar;D:\repository\com\uber\h3\3.7.3\h3-3.7.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java\jdk1.8.0_151\jre"/>
    <property name="basedir" value="C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate"/>
    <property name="file.separator" value="\"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="java.awt.graphicsenv" value="sun.awt.Win32GraphicsEnvironment"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire7067974933298881386\surefirebooter8688533736419783157.jar"/>
    <property name="sun.boot.class.path" value="C:\Program Files\Java\jdk1.8.0_151\jre\lib\resources.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\rt.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\sunrsasign.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\jsse.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\jce.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\charsets.jar;C:\Program Files\Java\jdk1.8.0_151\jre\lib\jfr.jar;C:\Program Files\Java\jdk1.8.0_151\jre\classes"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="1.8.0_151-b12"/>
    <property name="user.name" value="panda"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.endorsed.dirs" value="C:\Program Files\Java\jdk1.8.0_151\jre\lib\endorsed"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\repository"/>
    <property name="java.vendor.url.bug" value="http://bugreport.sun.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="1.8.0_151"/>
    <property name="user.dir" value="C:\Users\<USER>\Desktop\烟草\交接信息\源代码\ycwl-ms-v3.0\path-calculate"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="java.awt.printerjob" value="sun.awt.windows.WPrinterJob"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="java.library.path" value="C:\Program Files\Java\jdk1.8.0_151\jre\bin;C:\Windows\Sun\Java\bin;C:\Windows\system32;C:\Windows;D:\Python312\Scripts\;D:\Python312\;D:\VMware\bin\;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Oculus\Support\oculus-runtime;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA NvDLISR;C:\Program Files (x86)\Windows Kits\10\Windows Performance Toolkit\;C:\Program Files\Java\jdk1.8.0_151\bin;D:\qq\Bin;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\MySQL\MySQL Server 8.0\bin\;C:\Program Files\Java\jdk1.8.0_151\bin;D:\apache-tomcat-10.0.12\bin;D:\微信web开发者工具\dll;%M2_HO;E%\bin;D:\Redis\;D:\;D:\Git\cmd;D:\go\bin;D:\TortoiseSVN\bin;C:\Program Files (x86)\Graphviz2.38\bin;C:\Program Files\dotnet\;C:\Program Files\MySQL\MySQL Shell 8.0\bin\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\Bandizip\;D:\maven\apache-maven-3.6.3\bin;C:\Users\<USER>\AppData\Roaming\npm;D:\;C:\Users\<USER>\go\bin;F:\QQGame\Hall.58319\;E:\cursor\resources\app\bin;."/>
    <property name="java.vm.info" value="mixed mode"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="25.151-b12"/>
    <property name="java.ext.dirs" value="C:\Program Files\Java\jdk1.8.0_151\jre\lib\ext;C:\Windows\Sun\Java\lib\ext"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="52.0"/>
  </properties>
  <testcase name="testCoordinateAlignment" classname="com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest" time="0.001">
    <error message="Failed to load ApplicationContext" type="java.lang.IllegalStateException">java.lang.IllegalStateException: Failed to load ApplicationContext
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicDatasourceController': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
</error>
    <system-out><![CDATA[22:58:52,375 |-INFO in ch.qos.logback.classic.LoggerContext[default] - Found resource [logback-test.xml] at [file:/C:/Users/<USER>/Desktop/%E7%83%9F%E8%8D%89/%E4%BA%A4%E6%8E%A5%E4%BF%A1%E6%81%AF/%E6%BA%90%E4%BB%A3%E7%A0%81/ycwl-ms-v3.0/path-calculate/target/test-classes/logback-test.xml]
22:58:52,438 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - debug attribute not set
22:58:52,443 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.core.WorkloadBalancedKMeans] to OFF
22:58:52,443 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.MILPProblem] to INFO
22:58:52,443 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.milp.UnifiedConstraintModel] to INFO
22:58:52,443 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.jsprit.JSPRITVRPOptimizer] to INFO
22:58:52,443 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.fallback] to INFO
22:58:52,443 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization.adjustment] to INFO
22:58:52,443 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.ConsoleAppender]
22:58:52,446 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [CONSOLE]
22:58:52,467 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
22:58:52,470 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [TEST_FILE]
22:58:52,479 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1239807799 - No compression will be used
22:58:52,479 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1239807799 - Will use the pattern F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/test-execution.%d{yyyy-MM-dd}.%i.log for the active file
22:58:52,481 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5c671d7f - The date pattern is 'yyyy-MM-dd' from file name pattern 'F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/test-execution.%d{yyyy-MM-dd}.%i.log'.
22:58:52,481 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5c671d7f - Roll-over at midnight.
22:58:52,483 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5c671d7f - Setting initial period to Thu Aug 07 17:29:33 CST 2025
22:58:52,484 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5c671d7f - SizeAndTimeBasedFNATP is deprecated. Use SizeAndTimeBasedRollingPolicy instead
22:58:52,484 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@5c671d7f - For more information see http://logback.qos.ch/manual/appenders.html#SizeAndTimeBasedRollingPolicy
22:58:52,486 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[TEST_FILE] - Active log file name: F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/test-execution.log
22:58:52,486 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[TEST_FILE] - File property is set to [F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/test-execution.log]
22:58:52,487 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
22:58:52,487 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [ALGORITHM_FILE]
22:58:52,488 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1970436060 - No compression will be used
22:58:52,488 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1970436060 - Will use the pattern F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/algorithm-debug.%d{yyyy-MM-dd}.%i.log for the active file
22:58:52,488 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@687e99d8 - The date pattern is 'yyyy-MM-dd' from file name pattern 'F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/algorithm-debug.%d{yyyy-MM-dd}.%i.log'.
22:58:52,488 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@687e99d8 - Roll-over at midnight.
22:58:52,488 |-INFO in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@687e99d8 - Setting initial period to Thu Aug 07 17:29:32 CST 2025
22:58:52,488 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@687e99d8 - SizeAndTimeBasedFNATP is deprecated. Use SizeAndTimeBasedRollingPolicy instead
22:58:52,488 |-WARN in ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP@687e99d8 - For more information see http://logback.qos.ch/manual/appenders.html#SizeAndTimeBasedRollingPolicy
22:58:52,490 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[ALGORITHM_FILE] - Active log file name: F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/algorithm-debug.log
22:58:52,490 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[ALGORITHM_FILE] - File property is set to [F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/algorithm-debug.log]
22:58:52,490 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
22:58:52,490 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [TEST_CLASS_FILE]
22:58:52,490 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@239372207 - No compression will be used
22:58:52,490 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@239372207 - Will use the pattern F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/PathPlanningUtilsTest.%d{yyyy-MM-dd-HH}.log for the active file
22:58:52,491 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd-HH' from file name pattern 'F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/PathPlanningUtilsTest.%d{yyyy-MM-dd-HH}.log'.
22:58:52,491 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at the top of every hour.
22:58:52,491 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to Thu Aug 07 17:29:32 CST 2025
22:58:52,491 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[TEST_CLASS_FILE] - Active log file name: F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/PathPlanningUtilsTest.log
22:58:52,491 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[TEST_CLASS_FILE] - File property is set to [F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/PathPlanningUtilsTest.log]
22:58:52,492 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - About to instantiate appender of type [ch.qos.logback.core.rolling.RollingFileAppender]
22:58:52,492 |-INFO in ch.qos.logback.core.joran.action.AppenderAction - Naming appender as [CLUSTERING_POST_OPT_FILE]
22:58:52,493 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1789718525 - No compression will be used
22:58:52,493 |-INFO in c.q.l.core.rolling.TimeBasedRollingPolicy@1789718525 - Will use the pattern F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/clustering-post-optimization.%d{yyyy-MM-dd-HH}.log for the active file
22:58:52,493 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - The date pattern is 'yyyy-MM-dd-HH' from file name pattern 'F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/clustering-post-optimization.%d{yyyy-MM-dd-HH}.log'.
22:58:52,493 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Roll-over at the top of every hour.
22:58:52,495 |-INFO in c.q.l.core.rolling.DefaultTimeBasedFileNamingAndTriggeringPolicy - Setting initial period to Thu Aug 07 17:28:49 CST 2025
22:58:52,496 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[CLUSTERING_POST_OPT_FILE] - Active log file name: F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/clustering-post-optimization.log
22:58:52,496 |-INFO in ch.qos.logback.core.rolling.RollingFileAppender[CLUSTERING_POST_OPT_FILE] - File property is set to [F:/Code/ycwl/ycwl-ms-v3.0/path-calculate/target/test-results/algorithm/logs/clustering-post-optimization.log]
22:58:52,496 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtilsTest] to DEBUG
22:58:52,496 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting additivity of logger [com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtilsTest] to false
22:58:52,496 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [CONSOLE] to Logger[com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtilsTest]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [TEST_CLASS_FILE] to Logger[com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtilsTest]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [TEST_FILE] to Logger[com.ict.ycwl.pathcalculate.algorithm.PathPlanningUtilsTest]
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm] to DEBUG
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting additivity of logger [com.ict.ycwl.pathcalculate.algorithm] to false
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [CONSOLE] to Logger[com.ict.ycwl.pathcalculate.algorithm]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [ALGORITHM_FILE] to Logger[com.ict.ycwl.pathcalculate.algorithm]
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization] to DEBUG
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting additivity of logger [com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization] to false
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [CONSOLE] to Logger[com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [CLUSTERING_POST_OPT_FILE] to Logger[com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [ALGORITHM_FILE] to Logger[com.ict.ycwl.pathcalculate.algorithm.clustering_post_optimization]
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [org.springframework.boot.test] to INFO
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [org.springframework.test] to INFO
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting level of logger [org.optaplanner] to DEBUG
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.LoggerAction - Setting additivity of logger [org.optaplanner] to false
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [CONSOLE] to Logger[org.optaplanner]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [ALGORITHM_FILE] to Logger[org.optaplanner]
22:58:52,497 |-INFO in ch.qos.logback.classic.joran.action.RootLoggerAction - Setting level of ROOT logger to INFO
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [CONSOLE] to Logger[ROOT]
22:58:52,497 |-INFO in ch.qos.logback.core.joran.action.AppenderRefAction - Attaching appender named [TEST_FILE] to Logger[ROOT]
22:58:52,498 |-ERROR in ch.qos.logback.core.joran.spi.Interpreter@113:32 - no applicable action for [springProfile], current ElementPath  is [[configuration][springProfile]]
22:58:52,498 |-ERROR in ch.qos.logback.core.joran.spi.Interpreter@114:66 - no applicable action for [logger], current ElementPath  is [[configuration][springProfile][logger]]
22:58:52,498 |-INFO in ch.qos.logback.classic.joran.action.ConfigurationAction - End of configuration.
22:58:52,499 |-INFO in ch.qos.logback.classic.joran.JoranConfigurator@c86b9e3 - Registering current configuration as safe fallback point

2025-08-07 22:58:52.564 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest], using SpringBootContextLoader
2025-08-07 22:58:52.571 [main] INFO  o.s.test.context.support.AbstractContextLoader - Could not detect default resource locations for test class [com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2025-08-07 22:58:52.573 [main] INFO  o.s.t.c.support.AnnotationConfigContextLoaderUtils - Could not detect default configuration classes for test class [com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest]: CoordinateAlignmentTest does not declare any static, non-private, non-final, nested classes annotated with @Configuration.
2025-08-07 22:58:52.689 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Found @SpringBootConfiguration com.ict.ycwl.pathcalculate.PathCalculateApplication for test class com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest
2025-08-07 22:58:52.794 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2025-08-07 22:58:52.809 [main] INFO  o.s.b.t.context.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@46b61c56, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@2e48362c, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@1efe439d, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@be68757, org.springframework.test.context.support.DirtiesContextTestExecutionListener@7d446ed1, org.springframework.test.context.transaction.TransactionalTestExecutionListener@12d2ce03, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@7e5c856f, org.springframework.test.context.event.EventPublishingTestExecutionListener@413f69cc, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@1f53a5dc, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@1b75c2e3, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@1984b1f, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@3bd323e9, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@39ac0c0a, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@c7ba306]
2025-08-07 22:58:54.979 [background-preinit] INFO  org.hibernate.validator.internal.util.Version - HV000001: Hibernate Validator 6.1.7.Final

  .   ____          _            __ _ _
 /\\ / ___'_ __ _ _(_)_ __  __ _ \ \ \ \
( ( )\___ | '_ | '_| | '_ \/ _` | \ \ \ \
 \\/  ___)| |_)| | | | | || (_| |  ) ) ) )
  '  |____| .__|_| |_|_| |_\__, | / / / /
 =========|_|==============|___/=/_/_/_/
 :: Spring Boot ::        (v2.3.9.RELEASE)

2025-08-07 22:58:57.364 [main] INFO  c.i.y.p.converter.CoordinateAlignmentTest - No active profile set, falling back to default profiles: default
2025-08-07 22:58:58.619 [main] INFO  o.springframework.cloud.context.scope.GenericScope - BeanFactory id=98af3e26-6aa8-373a-85c2-bfc664133a64
2025-08-07 22:58:59.062 [main] INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig - 鈿欙笍 鏃堕棿璇勪及閰嶇疆:
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    鈴� 鏃堕棿绾︽潫: 鏈�澶�7.0h, 鏈�浼�6.0h, 鍙姩鍖洪棿0.5h
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    馃殫 琛岄┒閫熷害: 40.0km/h (缁熶竴閫熷害)
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    馃幆 鍐崇瓥闃堝��: 绔嬪嵆鍋滄95.0%, 璋ㄦ厧85.0%, 鏈�浼�75.0%
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    鈿栵笍 鏉冮噸閰嶇疆: 鏃堕棿70.0%, 鏁堢巼20.0%, 鍧囪　10.0%
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.TimeEvaluationConfig -    馃摑 娉細閰嶉�佹椂闂寸洿鎺ヤ娇鐢ˋccumulation.deliveryTime瀛楁
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.a.core.TimeBasedTerminationEvaluator - 鉁� TimeBasedTerminationEvaluator鍒濆鍖栧畬鎴�
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 馃攳 UnifiedClusteringAdapter甯﹀弬鏋勯�犲嚱鏁帮細鏃堕棿璇勪及鍣ㄧ姸鎬� = true
2025-08-07 22:58:59.063 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 馃攳 UnifiedClusteringAdapter甯﹀弬鏋勯�犲嚱鏁帮細閰嶇疆鏈夋晥鎬� = true
2025-08-07 22:58:59.529 [main] INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 馃椇锔� H3GeographicClustering鍒濆鍖栨垚鍔燂紙鏃堕棿璇勪及妯″紡锛夛紝Uber H3搴撶増鏈�: null
2025-08-07 22:58:59.531 [main] INFO  c.i.y.p.algorithm.core.H3GeographicClustering - 鉁� 鏃堕棿璇勪及鍣ㄥ凡鍚敤锛歍imeBasedTerminationEvaluator[鏈�澶ф椂闂�: 7.0h, 鍙姩鍖洪棿: 0.5h, 閫熷害: 40km/h]
2025-08-07 22:58:59.531 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter - 馃敡 UnifiedClusteringAdapter鍒濆鍖栧畬鎴愶紙浣跨敤澶栭儴璐ㄩ噺璇勪及鍣級
2025-08-07 22:58:59.531 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - H3鍏竟褰㈢綉鏍艰仛绫�: 鉁� 宸插姞杞�
2025-08-07 22:58:59.532 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - K-means宸ヤ綔閲忓潎琛¤仛绫�: 鉁� 宸插姞杞�
2025-08-07 22:58:59.532 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 閰嶇疆绠楁硶绫诲瀷: H3
2025-08-07 22:58:59.532 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 鑷姩闄嶇骇: 鍚敤
2025-08-07 22:58:59.532 [main] INFO  c.i.y.p.algorithm.core.UnifiedClusteringAdapter -    - 鎬ц兘瀵规瘮妯″紡: 绂佺敤
2025-08-07 22:58:59.540 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.540 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.540 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃殌 [鎵嬪姩鍔犺浇] 寮�濮嬫墜鍔ㄥ姞杞絆R-Tools鍘熺敓搴�
2025-08-07 22:58:59.540 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃摝 [鎻愬彇DLL] 浠嶫AR涓彁鍙杍niortools.dll
2025-08-07 22:58:59.541 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃搷 [Classpath] 鍦╟lasspath涓壘鍒�: D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-07 22:58:59.541 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃幆 [鎵惧埌JAR] D:\repository\com\google\ortools\ortools-win32-x86-64\9.8.3296\ortools-win32-x86-64-9.8.3296.jar
2025-08-07 22:58:59.541 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃搫 [DLL淇℃伅] jniortools.dll澶у皬: 21976064 瀛楄妭
2025-08-07 22:58:59.541 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃搧 [涓存椂鐩綍] 鍒涘缓涓存椂鐩綍: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************
2025-08-07 22:58:59.644 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃摜 [鍐欏叆瀹屾垚] 宸插啓鍏� 21976064 瀛楄妭鍒� C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.646 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎻愬彇鎴愬姛] DLL鏂囦欢鎻愬彇瀹屾垚锛屽ぇ灏忔纭�: 21976064 瀛楄妭
2025-08-07 22:58:59.647 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎻愬彇鎴愬姛] DLL鏂囦欢鎻愬彇鍒�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.647 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃摜 [鍔犺浇DLL] 鎵嬪姩鍔犺浇DLL鏂囦欢: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.716 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃И [楠岃瘉鍔犺浇] 楠岃瘉OR-Tools鍘熺敓搴撴槸鍚︽纭姞杞�
2025-08-07 22:58:59.724 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [楠岃瘉鎴愬姛] RoutingIndexManager鍒涘缓鎴愬姛锛屽師鐢熷簱姝ｇ‘鍔犺浇
2025-08-07 22:58:59.724 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 馃帀 [鍔犺浇鎴愬姛] OR-Tools鍘熺敓搴撴墜鍔ㄥ姞杞芥垚鍔燂紒
2025-08-07 22:58:59.724 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.734 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.753 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.754 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.754 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.754 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.756 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.756 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.758 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.758 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.758 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.759 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.759 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.761 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:58:59.762 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:58:59.762 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:58:59.762 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.762 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.762 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.762 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.763 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.765 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.765 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.765 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.766 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.766 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.769 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.769 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.769 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.770 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.770 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.770 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.770 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.770 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.771 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.771 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.771 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.771 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.771 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.772 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.772 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.775 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.775 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.775 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.775 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.775 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.775 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:58:59.775 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:58:59.775 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:58:59.784 [main] INFO  c.i.y.p.algorithm.debug.DebugDataExporter - 鉁� DebugDataExporter宸查厤缃瓾3鏃堕棿璁＄畻鍣�
2025-08-07 22:58:59.861 [main] DEBUG c.i.y.p.a.c.milp.solver.SolverManager - 鉁� 娉ㄥ唽姹傝В鍣�: Apache Commons Math v3.6.1
2025-08-07 22:58:59.866 [main] DEBUG c.i.y.p.a.c.milp.solver.SolverManager - 鉁� 娉ㄥ唽姹傝В鍣�: Builtin Heuristic v1.0.0
2025-08-07 22:58:59.866 [main] INFO  c.i.y.p.a.c.milp.solver.SolverManager - 馃敡 宸叉敞鍐� 2 涓唴缃眰瑙ｅ櫒
2025-08-07 22:58:59.977 [main] INFO  c.i.y.p.a.c.core.ClusteringPostOptimizerImpl - 馃殌 鑱氱被浜屾浼樺寲鍣ㄥ垵濮嬪寲瀹屾垚
2025-08-07 22:58:59.985 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.985 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.985 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.985 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.985 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.987 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.988 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.988 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.988 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.989 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.989 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:58:59.989 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:58:59.989 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:58:59.990 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:58:59.990 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:58:59.990 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:58:59.990 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:58:59.990 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:58:59.990 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:58:59.993 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:58:59.993 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.002 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.002 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.002 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.002 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.002 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.004 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.005 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.005 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.005 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.008 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.008 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.010 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.010 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.010 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.010 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.010 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.012 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.012 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.012 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.018 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.018 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.018 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.018 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.018 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.021 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.021 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.021 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.021 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.021 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.049 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.049 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.049 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.049 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.049 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.051 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.052 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.052 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.052 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.052 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.061 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.061 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.061 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.061 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.061 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.062 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.062 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.062 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.062 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.062 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.062 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.062 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.063 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.063 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.063 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.064 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.064 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.065 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.065 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.065 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.065 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.065 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.065 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.065 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.066 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.069 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.069 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.069 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.069 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.069 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.070 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.070 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.076 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.076 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.076 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.076 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.076 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.078 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.078 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.078 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.079 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.079 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.079 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.084 [main] INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 寮�濮婳R-Tools棰勫垵濮嬪寲...
2025-08-07 22:59:00.682 [main] INFO  c.i.y.p.algorithm.core.ORToolsBootstrap - 馃帀 OR-Tools棰勫垵濮嬪寲鎴愬姛锛�
2025-08-07 22:59:00.682 [main] INFO  c.ict.ycwl.pathcalculate.algorithm.core.ORToolsTSP - 馃帀 OR-Tools宸叉垚鍔熷姞杞藉苟鍙敤锛�
2025-08-07 22:59:00.686 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.686 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.686 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.686 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.686 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.687 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.687 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.687 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.689 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.689 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.690 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.690 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.690 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.690 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.690 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.690 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.690 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.690 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.693 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 馃洝锔� [绫诲姞杞戒繚鎶 OR-Tools绫诲姞杞戒繚鎶ゅ櫒鍚姩 - 鎵цJNI鐜棰勪慨澶�
2025-08-07 22:59:00.693 [main] WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 鈿狅笍 [姹℃煋妫�娴媇 绫诲凡鍔犺浇浣嗘湭姹℃煋: com.google.ortools.constraintsolver.RoutingModel - 鍙兘閿欒繃浜嗕慨澶嶆椂鏈�
2025-08-07 22:59:00.693 [main] WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 鈿狅笍 [姹℃煋妫�娴媇 绫诲凡鍔犺浇浣嗘湭姹℃煋: com.google.ortools.constraintsolver.RoutingIndexManager - 鍙兘閿欒繃浜嗕慨澶嶆椂鏈�
2025-08-07 22:59:00.693 [main] WARN  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 鈿狅笍 [姹℃煋妫�娴媇 绫诲凡鍔犺浇浣嗘湭姹℃煋: com.google.ortools.Loader - 鍙兘閿欒繃浜嗕慨澶嶆椂鏈�
2025-08-07 22:59:00.693 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 鉁� [绫诲姞杞戒繚鎶 OR-Tools绫诲皻鏈姞杞斤紝鐜娓呮磥锛屾墽琛岄闃叉�NI淇
2025-08-07 22:59:00.694 [main] INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 馃敡 寮�濮婮NI淇鏈嶅姟...
2025-08-07 22:59:00.868 [main] INFO  c.i.y.pathcalculate.algorithm.core.JNIFixService - 鉁� JNI淇鏈嶅姟瀹屾垚 - 鍩虹鐜宸插噯澶囧氨缁紝灏嗚繘琛孫R-Tools瀹為檯娴嬭瘯
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 馃敡 [绫诲姞杞戒繚鎶 JNI棰勪慨澶嶅畬鎴愶紝缁撴灉: true
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 馃洝锔� [绫诲姞杞戒繚鎶 OR-Tools绫诲姞杞戒繚鎶ゅ櫒鍒濆鍖栧畬鎴� - 鐜宸插氨缁�
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard - 馃攳 [璇婃柇淇℃伅] OR-Tools绫诲姞杞戒繚鎶ゅ櫒鐘舵��:
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    淇濇姢鍣ㄥ凡鍒濆鍖�: true
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    OR-Tools绫诲凡姹℃煋: false
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    JNI淇鏈嶅姟鐘舵��: JNIFixService[fixed=true, orToolsWorking=false]
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -    鍏抽敭绫荤姸鎬佹鏌�:
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.Loader : 宸插姞杞�
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingIndexManager : 宸插姞杞�
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ORToolsClassLoadGuard -      com.google.ortools.constraintsolver.RoutingModel : 宸插姞杞�
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 馃攧 [鍙嶅皠OR-Tools] 寮�濮嬪畨鍏ㄥ垵濮嬪寲ReflectiveORToolsTSP
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 馃攳 [鍙嶅皠鍒濆鍖朷 寮�濮嬪畨鍏ㄥ姞杞絆R-Tools绫�...
2025-08-07 22:59:00.868 [main] INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 鉁� [鍙嶅皠鍒濆鍖朷 JNI搴撳姞杞芥垚鍔�
2025-08-07 22:59:00.869 [main] INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 馃帀 [鍙嶅皠鍒濆鍖朷 OR-Tools鍙嶅皠鍔熻兘楠岃瘉鎴愬姛锛�
2025-08-07 22:59:00.869 [main] INFO  c.i.y.p.algorithm.core.ReflectiveORToolsTSP - 馃帀 [鍙嶅皠OR-Tools] OR-Tools鍙嶅皠鍒濆鍖栨垚鍔燂紒宸插噯澶囧氨缁�
2025-08-07 22:59:00.872 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.872 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.872 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.872 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.872 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.873 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.873 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.873 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.873 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.873 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.873 [main] INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 寮�濮嬪叏闈㈡祴璇昈R-Tools鍔熻兘...
2025-08-07 22:59:00.878 [main] INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - OR-Tools鍏ㄥ姛鑳藉彲鐢�
2025-08-07 22:59:00.878 [main] INFO  c.i.y.p.algorithm.core.RobustORToolsTSP - 馃帀 OR-Tools瀹屽叏鍙敤锛屾敮鎸佹墍鏈夐珮绾у姛鑳�
2025-08-07 22:59:00.881 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.881 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.881 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.881 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.881 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.882 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.882 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.882 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.882 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.885 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.886 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.886 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.886 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.886 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.886 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.888 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.888 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.888 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.889 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.889 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.889 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.889 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.892 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.892 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.892 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.892 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.892 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.894 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.894 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.894 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.896 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.896 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.897 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.897 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.897 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.899 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.899 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.899 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.900 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.900 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.901 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.901 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.901 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.901 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.901 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.901 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.903 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.903 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.907 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.907 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.907 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.907 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.907 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.910 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.910 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.BranchAndBoundTSP - 馃М BranchAndBoundTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.910 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.911 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇锛堣嚜瀹氫箟fallback锛�
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃敡 [鎵嬪姩OR-Tools] 寮�濮嬪垵濮嬪寲ManualORToolsTSP
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃殌 [鎵嬪姩鍒濆鍖朷 浣跨敤鎵嬪姩鍔犺浇鍣ㄥ垵濮嬪寲OR-Tools
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsLoader - 鉁� [鎵嬪姩鍔犺浇] OR-Tools搴撳凡缁忓姞杞�: C:\Users\<USER>\AppData\Local\Temp\manual-ortools-*************\jniortools.dll
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃摝 [绫诲姞杞絔 鍔犺浇OR-Tools Java绫�
2025-08-07 22:59:00.911 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃И [鍔熻兘娴嬭瘯] 鎵ц鍩烘湰鍔熻兘娴嬭瘯
2025-08-07 22:59:00.913 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 鉁� [鍔熻兘娴嬭瘯] OR-Tools鍩烘湰鍔熻兘娴嬭瘯鎴愬姛
2025-08-07 22:59:00.913 [main] INFO  c.i.y.p.algorithm.core.ManualORToolsTSP - 馃帀 [鎵嬪姩OR-Tools] OR-Tools鎵嬪姩鍔犺浇鎴愬姛锛佸凡鍑嗗灏辩华
2025-08-07 22:59:00.913 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃攧 [SafeORToolsTSP] 浣跨敤ManualORToolsTSP浣滀负搴曞眰瀹炵幇
2025-08-07 22:59:00.913 [main] INFO  c.i.y.pathcalculate.algorithm.core.SafeORToolsTSP - 馃幆 [SafeORToolsTSP] OR-Tools鐘舵��: 鍙敤
2025-08-07 22:59:00.913 [main] INFO  c.i.y.p.algorithm.core.DynamicProgrammingTSP - 馃М DynamicProgrammingTSP鍒濆鍖� - OR-Tools浼樺厛绛栫暐鍚敤
2025-08-07 22:59:00.913 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃殌 TSP姹傝В鍣ㄧ鐞嗗櫒鍒濆鍖栧畬鎴愶紙楂樻�ц兘閰嶇疆锛�
2025-08-07 22:59:00.913 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃摝 闆嗘垚绗笁鏂瑰簱: OR-Tools 9.8.3296 (VRP+CP-SAT), JTS 1.19.0, Commons-Math 3.6.1
2025-08-07 22:59:00.913 [main] INFO  c.i.y.p.algorithm.core.TSPSolverManager - 馃幆 绠楁硶浼樺厛绾�: OR-Tools > 澧炲己閬椾紶绠楁硶 > 鍩虹绠楁硶
2025-08-07 22:59:00.996 [main] WARN  o.s.w.context.support.GenericWebApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicDatasourceController': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
2025-08-07 22:59:01.003 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLoggingListener - 

Error starting ApplicationContext. To display the conditions report re-run your application with 'debug' enabled.
2025-08-07 22:59:01.011 [main] ERROR org.springframework.boot.SpringApplication - Application run failed
org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicDatasourceController': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:405)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1380)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1255)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1255)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:178)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:124)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:175)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:918)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1248)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	... 82 common frames omitted
2025-08-07 22:59:01.012 [main] ERROR o.springframework.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.test.context.web.ServletTestExecutionListener@46b61c56] to prepare test instance [com.ict.ycwl.pathcalculate.converter.CoordinateAlignmentTest@4dfdfe7d]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:132)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:123)
	at org.springframework.test.context.web.ServletTestExecutionListener.setUpRequestContextIfNecessary(ServletTestExecutionListener.java:190)
	at org.springframework.test.context.web.ServletTestExecutionListener.prepareTestInstance(ServletTestExecutionListener.java:132)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:244)
	at org.springframework.test.context.junit.jupiter.SpringExtension.postProcessTestInstance(SpringExtension.java:98)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$5(ClassBasedTestDescriptor.java:341)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.executeAndMaskThrowable(ClassBasedTestDescriptor.java:346)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$invokeTestInstancePostProcessors$6(ClassBasedTestDescriptor.java:341)
	at java.util.stream.ReferencePipeline$3$1.accept(ReferencePipeline.java:193)
	at java.util.stream.ReferencePipeline$2$1.accept(ReferencePipeline.java:175)
	at java.util.ArrayList$ArrayListSpliterator.forEachRemaining(ArrayList.java:1380)
	at java.util.stream.AbstractPipeline.copyInto(AbstractPipeline.java:481)
	at java.util.stream.AbstractPipeline.wrapAndCopyInto(AbstractPipeline.java:471)
	at java.util.stream.StreamSpliterators$WrappingSpliterator.forEachRemaining(StreamSpliterators.java:312)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:743)
	at java.util.stream.Streams$ConcatSpliterator.forEachRemaining(Streams.java:742)
	at java.util.stream.ReferencePipeline$Head.forEach(ReferencePipeline.java:580)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.invokeTestInstancePostProcessors(ClassBasedTestDescriptor.java:340)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.instantiateAndPostProcessTestInstance(ClassBasedTestDescriptor.java:263)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$2(ClassBasedTestDescriptor.java:256)
	at java.util.Optional.orElseGet(Optional.java:267)
	at org.junit.jupiter.engine.descriptor.ClassBasedTestDescriptor.lambda$testInstancesProvider$3(ClassBasedTestDescriptor.java:255)
	at org.junit.jupiter.engine.execution.TestInstancesProvider.getTestInstances(TestInstancesProvider.java:29)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$prepare$0(TestMethodTestDescriptor.java:108)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:107)
	at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.prepare(TestMethodTestDescriptor.java:71)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$prepare$1(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.prepare(NodeTestTask.java:107)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:75)
	at java.util.ArrayList.forEach(ArrayList.java:1255)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at java.util.ArrayList.forEach(ArrayList.java:1255)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:38)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$5(NodeTestTask.java:139)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$7(NodeTestTask.java:125)
	at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:135)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:123)
	at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:122)
	at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:80)
	at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:32)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
	at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:51)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:220)
	at org.junit.platform.launcher.core.DefaultLauncher.lambda$execute$6(DefaultLauncher.java:188)
	at org.junit.platform.launcher.core.DefaultLauncher.withInterceptedStreams(DefaultLauncher.java:202)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:181)
	at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:128)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:150)
	at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:124)
	at org.apache.maven.surefire.booter.ForkedBooter.invokeProviderInSameClassLoader(ForkedBooter.java:384)
	at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:345)
	at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:126)
	at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:418)
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'dynamicDatasourceController': Injection of autowired dependencies failed; nested exception is java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:405)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1425)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:593)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:516)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:324)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:322)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:202)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:897)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:879)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:551)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:758)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:750)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:405)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:315)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:120)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	... 65 common frames omitted
Caused by: java.lang.IllegalArgumentException: Could not resolve placeholder 'jjking.dbPath' in value "${jjking.dbPath}"
	at org.springframework.util.PropertyPlaceholderHelper.parseStringValue(PropertyPlaceholderHelper.java:178)
	at org.springframework.util.PropertyPlaceholderHelper.replacePlaceholders(PropertyPlaceholderHelper.java:124)
	at org.springframework.core.env.AbstractPropertyResolver.doResolvePlaceholders(AbstractPropertyResolver.java:239)
	at org.springframework.core.env.AbstractPropertyResolver.resolveRequiredPlaceholders(AbstractPropertyResolver.java:210)
	at org.springframework.context.support.PropertySourcesPlaceholderConfigurer.lambda$processProperties$0(PropertySourcesPlaceholderConfigurer.java:175)
	at org.springframework.beans.factory.support.AbstractBeanFactory.resolveEmbeddedValue(AbstractBeanFactory.java:918)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1248)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1227)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.resolveFieldValue(AutowiredAnnotationBeanPostProcessor.java:657)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor$AutowiredFieldElement.inject(AutowiredAnnotationBeanPostProcessor.java:640)
	at org.springframework.beans.factory.annotation.InjectionMetadata.inject(InjectionMetadata.java:119)
	at org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor.postProcessProperties(AutowiredAnnotationBeanPostProcessor.java:399)
	... 82 common frames omitted
]]></system-out>
  </testcase>
</testsuite>