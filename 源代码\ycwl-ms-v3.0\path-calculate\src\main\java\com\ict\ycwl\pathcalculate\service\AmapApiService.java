package com.ict.ycwl.pathcalculate.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.concurrent.TimeUnit;

/**
 * 高德地图API服务
 * 用于获取真实的点对点行驶时间和距离
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 */
@Slf4j
@Service
public class AmapApiService {
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    // 高德地图API配置
    private static final String AMAP_DIRECTION_URL = "https://restapi.amap.com/v3/direction/driving";
    private static final int MAX_RETRIES = 3;
    private static final long RETRY_DELAY_MS = 1000; // 1秒
    private static final long API_RATE_LIMIT_MS = 100; // API调用间隔100ms
    
    @Value("${amap.api.key:3729e38b382749ba3a10bae7539e0d9a}")
    private String apiKey;
    
    private long lastApiCallTime = 0;
    
    public AmapApiService() {
        this.restTemplate = new RestTemplate();
        this.objectMapper = new ObjectMapper();
    }
    
    /**
     * 获取两点间的行驶时间和距离
     * 
     * @param fromLng 起点经度
     * @param fromLat 起点纬度
     * @param toLng 终点经度
     * @param toLat 终点纬度
     * @return 路径信息，包含行驶时间（分钟）和距离（公里）
     */
    public RouteInfo getRouteInfo(double fromLng, double fromLat, double toLng, double toLat) {
        // API调用频率限制
        rateLimitDelay();
        
        String origin = String.format("%.6f,%.6f", fromLng, fromLat);
        String destination = String.format("%.6f,%.6f", toLng, toLat);
        
        for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
            try {
                String url = buildApiUrl(origin, destination);
                log.debug("调用高德API: {} -> {}, 尝试第{}次", origin, destination, attempt);
                
                String response = restTemplate.getForObject(url, String.class);
                RouteInfo routeInfo = parseResponse(response, origin, destination);
                
                if (routeInfo != null) {
                    log.debug("API调用成功: {} -> {}, 时间={}分钟, 距离={}公里", 
                            origin, destination, routeInfo.getTravelTime(), routeInfo.getDistance());
                    return routeInfo;
                }
                
            } catch (Exception e) {
                log.warn("高德API调用失败 (尝试{}/{}): {} -> {}, 错误: {}", 
                        attempt, MAX_RETRIES, origin, destination, e.getMessage());
                
                if (attempt < MAX_RETRIES) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }
        
        // 所有重试都失败，返回基于距离的估算值
        log.warn("高德API调用最终失败，使用距离估算: {} -> {}", origin, destination);
        return estimateRouteInfo(fromLng, fromLat, toLng, toLat);
    }
    
    /**
     * 构建API请求URL
     */
    private String buildApiUrl(String origin, String destination) {
        return String.format("%s?key=%s&origin=%s&destination=%s&strategy=0&extensions=base",
                AMAP_DIRECTION_URL, apiKey, origin, destination);
    }
    
    /**
     * 解析高德API响应
     */
    private RouteInfo parseResponse(String response, String origin, String destination) {
        try {
            JsonNode root = objectMapper.readTree(response);
            
            // 检查API响应状态
            String status = root.path("status").asText();
            if (!"1".equals(status)) {
                String info = root.path("info").asText();
                log.warn("高德API返回错误: status={}, info={}", status, info);
                return null;
            }
            
            // 解析路径信息
            JsonNode route = root.path("route");
            JsonNode paths = route.path("paths");
            
            if (paths.isArray() && paths.size() > 0) {
                JsonNode firstPath = paths.get(0);
                
                // 获取距离（米）和时间（秒）
                double distanceMeters = firstPath.path("distance").asDouble();
                double durationSeconds = firstPath.path("duration").asDouble();
                
                // 转换单位：距离转为公里，时间转为分钟
                double distanceKm = distanceMeters / 1000.0;
                double durationMinutes = durationSeconds / 60.0;
                
                return new RouteInfo(durationMinutes, distanceKm, true);
            }
            
            log.warn("高德API响应中没有找到路径信息: {}", response);
            return null;
            
        } catch (Exception e) {
            log.error("解析高德API响应失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 基于直线距离估算路径信息
     */
    private RouteInfo estimateRouteInfo(double fromLng, double fromLat, double toLng, double toLat) {
        double distance = calculateHaversineDistance(fromLng, fromLat, toLng, toLat);
        
        // 估算参数：道路系数1.3，平均速度40km/h
        double roadDistance = distance * 1.3;
        double travelTime = roadDistance / 40.0 * 60.0; // 转换为分钟
        
        return new RouteInfo(travelTime, roadDistance, false);
    }
    
    /**
     * 计算两点间的球面距离（Haversine公式）
     */
    private double calculateHaversineDistance(double lng1, double lat1, double lng2, double lat2) {
        final double R = 6371.0; // 地球半径（公里）
        
        double dLat = Math.toRadians(lat2 - lat1);
        double dLng = Math.toRadians(lng2 - lng1);
        
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                   Math.cos(Math.toRadians(lat1)) * Math.cos(Math.toRadians(lat2)) *
                   Math.sin(dLng / 2) * Math.sin(dLng / 2);
        
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        
        return R * c;
    }
    
    /**
     * API调用频率限制
     */
    private void rateLimitDelay() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastCall = currentTime - lastApiCallTime;
        
        if (timeSinceLastCall < API_RATE_LIMIT_MS) {
            try {
                Thread.sleep(API_RATE_LIMIT_MS - timeSinceLastCall);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        lastApiCallTime = System.currentTimeMillis();
    }
    
    /**
     * 路径信息数据结构
     */
    public static class RouteInfo {
        private final double travelTime; // 行驶时间（分钟）
        private final double distance;   // 距离（公里）
        private final boolean fromApi;   // 是否来自API（true）还是估算（false）
        
        public RouteInfo(double travelTime, double distance, boolean fromApi) {
            this.travelTime = Math.max(0.5, travelTime); // 最小0.5分钟
            this.distance = Math.max(0.01, distance);    // 最小0.01公里
            this.fromApi = fromApi;
        }
        
        public double getTravelTime() { return travelTime; }
        public double getDistance() { return distance; }
        public boolean isFromApi() { return fromApi; }
        
        @Override
        public String toString() {
            return String.format("RouteInfo{time=%.2f分钟, distance=%.2fkm, fromApi=%s}", 
                    travelTime, distance, fromApi);
        }
    }
}
