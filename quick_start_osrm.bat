@echo off
echo ========================================
echo 快速启动OSRM服务 - 使用预构建数据
echo ========================================

echo 1. 检查Docker是否运行...
docker --version
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动
    echo 请先安装Docker Desktop: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

echo 2. 停止并清理旧容器...
docker stop osrm-backend 2>nul
docker rm osrm-backend 2>nul

echo 3. 启动OSRM服务 (使用内置数据)...
echo 这将使用OSRM容器内置的Berlin数据进行测试
echo OSRM服务将在 http://localhost:5000 启动

docker run -d --name osrm-backend -p 5000:5000 osrm/osrm-backend

echo 4. 等待服务启动...
timeout /t 10 /nobreak

echo 5. 测试服务是否可用...
curl -s "http://localhost:5000/route/v1/driving/13.388860,52.517037;13.397634,52.529407?overview=false"
if %errorlevel% equ 0 (
    echo.
    echo ✅ OSRM服务启动成功！
    echo 服务地址: http://localhost:5000
    echo.
    echo 现在可以重启Java服务并测试前辈的方法了！
    echo.
    echo 要停止OSRM服务，运行: docker stop osrm-backend
) else (
    echo.
    echo ❌ OSRM服务启动失败
    echo 查看容器日志: docker logs osrm-backend
)

pause
