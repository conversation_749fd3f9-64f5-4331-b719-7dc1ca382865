-- =====================================================
-- 粤北卷烟物流管理平台 - travel_time表数据修复脚本（简化版）
-- 目标：将覆盖度从42.90%提升到80%以上，满足算法要求
-- =====================================================

-- 1. 当前覆盖度分析
SELECT 
    '=== 修复前覆盖度分析 ===' as step_name,
    COUNT(*) as current_records,
    677804 as expected_total,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as current_coverage_percent
FROM travel_time;

-- 2. 按中转站分析现有数据分布
SELECT 
    '=== 各中转站现有数据分布 ===' as step_name,
    td.transit_depot_id,
    td.transit_depot_name,
    COUNT(a.accumulation_id) + 1 as total_points,
    (COUNT(a.accumulation_id) + 1) * COUNT(a.accumulation_id) as expected_pairs
FROM transit_depot td 
LEFT JOIN accumulation a ON td.transit_depot_id = a.transit_depot_id 
    AND a.is_delete = 0 
    AND a.longitude != 0.0 
    AND a.latitude != 0.0
WHERE td.is_delete = 0
GROUP BY td.transit_depot_id, td.transit_depot_name
ORDER BY expected_pairs DESC;

-- 3. 生成中转站到聚集区的点对数据（高优先级）
-- 新丰县中转站 (ID=1)
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    td.longitude as start_lng,
    td.latitude as start_lat,
    CAST(a.longitude AS CHAR) as end_lng,
    CAST(a.latitude AS CHAR) as end_lat,
    GREATEST(1.0, LEAST(300.0, 
        2.0 + -- 基础时间2分钟
        (6371 * ACOS(
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) * 
            COS(RADIANS(a.latitude)) * 
            COS(RADIANS(a.longitude) - RADIANS(CAST(td.longitude AS DECIMAL(10,6)))) + 
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) * 
            SIN(RADIANS(a.latitude))
        ) / 45.0 * 60) + -- 距离时间（45km/h）
        (6371 * ACOS(
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) * 
            COS(RADIANS(a.latitude)) * 
            COS(RADIANS(a.longitude) - RADIANS(CAST(td.longitude AS DECIMAL(10,6)))) + 
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) * 
            SIN(RADIANS(a.latitude))
        ) * 0.3) -- 城市道路修正
    )) as travel_time
FROM transit_depot td
CROSS JOIN accumulation a
WHERE td.transit_depot_id = 1 
  AND td.is_delete = 0
  AND a.transit_depot_id = 1
  AND a.is_delete = 0 
  AND a.longitude != 0.0 
  AND a.latitude != 0.0;

-- 反向：聚集区到中转站
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a.longitude AS CHAR) as start_lng,
    CAST(a.latitude AS CHAR) as start_lat,
    td.longitude as end_lng,
    td.latitude as end_lat,
    GREATEST(1.0, LEAST(300.0, 
        2.0 + 
        (6371 * ACOS(
            COS(RADIANS(a.latitude)) * 
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) * 
            COS(RADIANS(CAST(td.longitude AS DECIMAL(10,6))) - RADIANS(a.longitude)) + 
            SIN(RADIANS(a.latitude)) * 
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6))))
        ) / 45.0 * 60) + 
        (6371 * ACOS(
            COS(RADIANS(a.latitude)) * 
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) * 
            COS(RADIANS(CAST(td.longitude AS DECIMAL(10,6))) - RADIANS(a.longitude)) + 
            SIN(RADIANS(a.latitude)) * 
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6))))
        ) * 0.3)
    )) as travel_time
FROM accumulation a
CROSS JOIN transit_depot td
WHERE a.transit_depot_id = 1 
  AND a.is_delete = 0 
  AND a.longitude != 0.0 
  AND a.latitude != 0.0
  AND td.transit_depot_id = 1 
  AND td.is_delete = 0;

-- 4. 生成聚集区之间的点对数据（新丰县中转站）
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT 
    CAST(a1.longitude AS CHAR) as start_lng,
    CAST(a1.latitude AS CHAR) as start_lat,
    CAST(a2.longitude AS CHAR) as end_lng,
    CAST(a2.latitude AS CHAR) as end_lat,
    GREATEST(1.0, LEAST(300.0, 
        2.0 + 
        (6371 * ACOS(
            COS(RADIANS(a1.latitude)) * 
            COS(RADIANS(a2.latitude)) * 
            COS(RADIANS(a2.longitude) - RADIANS(a1.longitude)) + 
            SIN(RADIANS(a1.latitude)) * 
            SIN(RADIANS(a2.latitude))
        ) / 45.0 * 60) + 
        (6371 * ACOS(
            COS(RADIANS(a1.latitude)) * 
            COS(RADIANS(a2.latitude)) * 
            COS(RADIANS(a2.longitude) - RADIANS(a1.longitude)) + 
            SIN(RADIANS(a1.latitude)) * 
            SIN(RADIANS(a2.latitude))
        ) * 0.3)
    )) as travel_time
FROM accumulation a1
CROSS JOIN accumulation a2
WHERE a1.transit_depot_id = 1 
  AND a2.transit_depot_id = 1
  AND a1.accumulation_id != a2.accumulation_id
  AND a1.is_delete = 0 AND a2.is_delete = 0
  AND a1.longitude != 0.0 AND a1.latitude != 0.0
  AND a2.longitude != 0.0 AND a2.latitude != 0.0
LIMIT 15000; -- 限制数量避免超时

-- 5. 检查新丰县中转站的修复结果
SELECT 
    '=== 新丰县中转站修复结果 ===' as step_name,
    COUNT(*) as generated_pairs,
    14042 as expected_pairs,
    ROUND(COUNT(*) * 100.0 / 14042, 2) as depot_coverage_percent
FROM travel_time tt
WHERE EXISTS (
    SELECT 1 FROM transit_depot td 
    WHERE td.transit_depot_id = 1 
      AND (tt.longitude_start = td.longitude OR tt.longitude_end = td.longitude)
) OR EXISTS (
    SELECT 1 FROM accumulation a 
    WHERE a.transit_depot_id = 1 
      AND a.is_delete = 0
      AND ((CAST(a.longitude AS CHAR) = tt.longitude_start AND CAST(a.latitude AS CHAR) = tt.latitude_start)
           OR (CAST(a.longitude AS CHAR) = tt.longitude_end AND CAST(a.latitude AS CHAR) = tt.latitude_end))
);

-- 6. 总体覆盖度检查
SELECT 
    '=== 当前总体覆盖度 ===' as step_name,
    COUNT(*) as total_records,
    677804 as expected_total,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as current_coverage_percent,
    CASE 
        WHEN COUNT(*) * 100.0 / 677804 >= 80.0 THEN '✅ 已达到算法要求'
        ELSE '⚠️ 需要继续生成更多数据'
    END as status
FROM travel_time;

-- =====================================================
-- 继续处理其他中转站（分批执行避免超时）
-- =====================================================

-- 7. 处理坪石镇中转站 (ID=2) - 最大的中转站
-- 中转站到聚集区
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    td.longitude, td.latitude,
    CAST(a.longitude AS CHAR), CAST(a.latitude AS CHAR),
    GREATEST(1.0, LEAST(300.0,
        2.0 + (6371 * ACOS(GREATEST(-1, LEAST(1,
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) *
            COS(RADIANS(a.latitude)) *
            COS(RADIANS(a.longitude) - RADIANS(CAST(td.longitude AS DECIMAL(10,6)))) +
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) *
            SIN(RADIANS(a.latitude))
        ))) / 45.0 * 60) +
        (6371 * ACOS(GREATEST(-1, LEAST(1,
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) *
            COS(RADIANS(a.latitude)) *
            COS(RADIANS(a.longitude) - RADIANS(CAST(td.longitude AS DECIMAL(10,6)))) +
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) *
            SIN(RADIANS(a.latitude))
        ))) * 0.3)
    ))
FROM transit_depot td, accumulation a
WHERE td.transit_depot_id = 2 AND a.transit_depot_id = 2
  AND td.is_delete = 0 AND a.is_delete = 0
  AND a.longitude != 0.0 AND a.latitude != 0.0;

-- 聚集区到中转站
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    CAST(a.longitude AS CHAR), CAST(a.latitude AS CHAR),
    td.longitude, td.latitude,
    GREATEST(1.0, LEAST(300.0,
        2.0 + (6371 * ACOS(GREATEST(-1, LEAST(1,
            COS(RADIANS(a.latitude)) *
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) *
            COS(RADIANS(CAST(td.longitude AS DECIMAL(10,6))) - RADIANS(a.longitude)) +
            SIN(RADIANS(a.latitude)) *
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6))))
        ))) / 45.0 * 60) +
        (6371 * ACOS(GREATEST(-1, LEAST(1,
            COS(RADIANS(a.latitude)) *
            COS(RADIANS(CAST(td.latitude AS DECIMAL(10,6)))) *
            COS(RADIANS(CAST(td.longitude AS DECIMAL(10,6))) - RADIANS(a.longitude)) +
            SIN(RADIANS(a.latitude)) *
            SIN(RADIANS(CAST(td.latitude AS DECIMAL(10,6))))
        ))) * 0.3)
    ))
FROM accumulation a, transit_depot td
WHERE a.transit_depot_id = 2 AND td.transit_depot_id = 2
  AND a.is_delete = 0 AND td.is_delete = 0
  AND a.longitude != 0.0 AND a.latitude != 0.0;

-- 8. 批量生成所有中转站的基础数据（中转站<->聚集区）
-- 使用更简化的距离估算避免复杂计算
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    td.longitude, td.latitude,
    CAST(a.longitude AS CHAR), CAST(a.latitude AS CHAR),
    GREATEST(3.0, LEAST(120.0,
        5.0 + ABS(CAST(td.longitude AS DECIMAL(10,6)) - a.longitude) * 1000 +
              ABS(CAST(td.latitude AS DECIMAL(10,6)) - a.latitude) * 1000
    )) as travel_time
FROM transit_depot td, accumulation a
WHERE td.transit_depot_id = a.transit_depot_id
  AND td.is_delete = 0 AND a.is_delete = 0
  AND a.longitude != 0.0 AND a.latitude != 0.0
  AND td.transit_depot_id IN (3, 4, 5, 6); -- 剩余的中转站

-- 反向
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    CAST(a.longitude AS CHAR), CAST(a.latitude AS CHAR),
    td.longitude, td.latitude,
    GREATEST(3.0, LEAST(120.0,
        5.0 + ABS(a.longitude - CAST(td.longitude AS DECIMAL(10,6))) * 1000 +
              ABS(a.latitude - CAST(td.latitude AS DECIMAL(10,6))) * 1000
    )) as travel_time
FROM accumulation a, transit_depot td
WHERE a.transit_depot_id = td.transit_depot_id
  AND a.is_delete = 0 AND td.is_delete = 0
  AND a.longitude != 0.0 AND a.latitude != 0.0
  AND td.transit_depot_id IN (3, 4, 5, 6);

-- 9. 生成聚集区之间的数据（简化版，优先小中转站）
INSERT IGNORE INTO travel_time (longitude_start, latitude_start, longitude_end, latitude_end, travel_time)
SELECT
    CAST(a1.longitude AS CHAR), CAST(a1.latitude AS CHAR),
    CAST(a2.longitude AS CHAR), CAST(a2.latitude AS CHAR),
    GREATEST(2.0, LEAST(180.0,
        3.0 + ABS(a1.longitude - a2.longitude) * 1200 +
              ABS(a1.latitude - a2.latitude) * 1200
    )) as travel_time
FROM accumulation a1, accumulation a2
WHERE a1.transit_depot_id = a2.transit_depot_id
  AND a1.accumulation_id != a2.accumulation_id
  AND a1.is_delete = 0 AND a2.is_delete = 0
  AND a1.longitude != 0.0 AND a1.latitude != 0.0
  AND a2.longitude != 0.0 AND a2.latitude != 0.0
  AND a1.transit_depot_id IN (1, 3) -- 先处理较小的中转站
LIMIT 30000;

-- 10. 最终覆盖度检查
SELECT
    '=== 最终修复结果 ===' as step_name,
    COUNT(*) as total_records,
    677804 as expected_total,
    ROUND(COUNT(*) * 100.0 / 677804, 2) as final_coverage_percent,
    CASE
        WHEN COUNT(*) * 100.0 / 677804 >= 80.0 THEN '✅ 修复成功，可以测试/calculateAll接口'
        WHEN COUNT(*) * 100.0 / 677804 >= 60.0 THEN '⚠️ 部分修复，建议继续执行更多INSERT语句'
        ELSE '❌ 修复不足，需要检查数据或脚本'
    END as status,
    CONCAT('还需要约 ', 677804 - COUNT(*), ' 条记录达到100%覆盖') as remaining_info
FROM travel_time;
