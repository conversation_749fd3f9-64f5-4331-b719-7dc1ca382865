@echo off
echo ========================================
echo 启动OSRM服务 (端口5000) - 修复版
echo ========================================

echo 1. 检查Docker是否运行...
docker --version
if %errorlevel% neq 0 (
    echo 错误: Docker未安装或未启动
    echo 请先安装Docker Desktop: https://www.docker.com/products/docker-desktop/
    pause
    exit /b 1
)

echo 2. 清理旧数据...
if exist "*.osrm*" del /q *.osrm*
if exist "*.osm.pbf" del /q *.osm.pbf

echo 3. 下载广东省地图数据 (适合烟草项目)...
echo 正在下载广东省地图数据...
curl -L -o guangdong-latest.osm.pbf "http://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf"
if %errorlevel% neq 0 (
    echo 下载失败，尝试备用链接...
    curl -L -o guangdong-latest.osm.pbf "https://download.geofabrik.de/asia/china/guangdong-latest.osm.pbf"
    if %errorlevel% neq 0 (
        echo 所有下载都失败，创建最小测试数据...
        goto create_test_data
    )
)

echo 4. 预处理地图数据...
echo 步骤1: 提取路网数据...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-extract -p /opt/car.lua /data/guangdong-latest.osm.pbf
if %errorlevel% neq 0 (
    echo 提取失败，尝试创建测试数据...
    goto create_test_data
)

echo 步骤2: 分区处理...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-partition /data/guangdong-latest.osrm
if %errorlevel% neq 0 (
    echo 分区失败，尝试创建测试数据...
    goto create_test_data
)

echo 步骤3: 自定义处理...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-customize /data/guangdong-latest.osrm
if %errorlevel% neq 0 (
    echo 自定义失败，尝试创建测试数据...
    goto create_test_data
)

echo 5. 启动OSRM服务...
echo OSRM服务将在 http://localhost:5000 启动
echo 按 Ctrl+C 停止服务
docker run --rm -t -i -p 5000:5000 -v "%cd%:/data" osrm/osrm-backend osrm-routed --algorithm mld /data/guangdong-latest.osrm
goto end

:create_test_data
echo ========================================
echo 创建最小测试数据
echo ========================================
echo 正在创建包含广州-韶关路网的测试数据...

echo ^<?xml version="1.0" encoding="UTF-8"?^> > test.osm
echo ^<osm version="0.6" generator="test"^> >> test.osm
echo ^<node id="1" lat="23.1291" lon="113.2644"/^> >> test.osm
echo ^<node id="2" lat="24.8138" lon="113.5918"/^> >> test.osm
echo ^<node id="3" lat="25.2521" lon="113.5588"/^> >> test.osm
echo ^<way id="1"^> >> test.osm
echo ^<nd ref="1"/^> >> test.osm
echo ^<nd ref="2"/^> >> test.osm
echo ^<nd ref="3"/^> >> test.osm
echo ^<tag k="highway" v="trunk"/^> >> test.osm
echo ^</way^> >> test.osm
echo ^</osm^> >> test.osm

echo 预处理测试数据...
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-extract -p /opt/car.lua /data/test.osm
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-partition /data/test.osrm
docker run --rm -t -v "%cd%:/data" osrm/osrm-backend osrm-customize /data/test.osrm

echo 启动OSRM服务 (测试数据)...
echo OSRM服务将在 http://localhost:5000 启动
echo 按 Ctrl+C 停止服务
docker run --rm -t -i -p 5000:5000 -v "%cd%:/data" osrm/osrm-backend osrm-routed --algorithm mld /data/test.osrm

:end
pause
